import { NextRequest, NextResponse } from 'next/server';
import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';

export async function PATCH(
  request: NextRequest,
  { params }: { params: { serviceId: string } }
) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    const { serviceId } = params;
    const formData = await request.formData();

    // Log the form data for debugging
    const status = formData.get('status');
    const notes = formData.get('notes');
    const evidenceFiles = formData.getAll('evidence');

    console.log('🔗 Updating service progress:', {
      serviceId,
      status,
      notesLength: notes ? (notes as string).length : 0,
      evidenceCount: evidenceFiles.length,
      evidenceFiles: evidenceFiles.map(file =>
        file instanceof File ? { name: file.name, size: file.size, type: file.type } : 'Not a file'
      )
    });

    const response = await axios.patch(
      `${URL_API}/vendor-platform/corrective-maintenance/services/${serviceId}/progress`,
      formData,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    console.log('✅ Service progress updated successfully:', response.data);

    // Log the updated service data to see if evidence is included
    console.log('🔍 Updated service data:', {
      serviceId,
      updatedService: response.data.data || response.data,
      evidenceInResponse: (response.data.data || response.data)?.evidence?.length || 0,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json({
      success: true,
      data: response.data.data || response.data,
      message: response.data.message || 'Progreso del servicio actualizado exitosamente',
    });
  } catch (error: any) {
    console.error('❌ Error updating service progress:', {
      serviceId: params.serviceId,
      error: error.response?.data || error.message,
      status: error.response?.status,
      headers: error.response?.headers
    });

    return NextResponse.json(
      {
        success: false,
        message: error.response?.data?.message || 'Error al actualizar el progreso del servicio',
        details: error.response?.data || error.message,
      },
      { status: error.response?.status || 500 }
    );
  }
}
