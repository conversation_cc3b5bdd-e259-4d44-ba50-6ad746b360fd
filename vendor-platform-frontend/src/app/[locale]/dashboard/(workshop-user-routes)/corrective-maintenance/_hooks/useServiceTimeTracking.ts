'use client';

import { useState, useEffect, useCallback } from 'react';
import { CorrectiveService } from '../types';

export interface ServiceTimeData {
  serviceId: string;
  serviceName: string;
  status: string;
  estimatedDuration: number; // in hours
  actualDuration: number; // in hours
  timeRemaining: number; // in hours
  isOverdue: boolean;
  startTime?: Date;
  endTime?: Date;
  progressPercentage: number;
}

export interface OrderTimeData {
  orderId: string;
  totalEstimatedTime: number;
  totalActualTime: number;
  totalTimeRemaining: number;
  overallProgress: number;
  completedServices: number;
  inProgressServices: number;
  overdueServices: number;
  services: ServiceTimeData[];
}

export function useServiceTimeTracking(services: CorrectiveService[], orderId: string) {
  const [timeData, setTimeData] = useState<OrderTimeData | null>(null);
  const [isTracking, setIsTracking] = useState(false);

  const calculateServiceTime = useCallback((service: CorrectiveService): ServiceTimeData => {
    const estimatedDuration = service.estimatedDuration;
    let actualDuration = 0;
    let timeRemaining = estimatedDuration;
    let isOverdue = false;
    let startTime: Date | undefined;
    let endTime: Date | undefined;
    let progressPercentage = 0;

    // Calculate actual duration based on service status and timestamps
    if (service.status === 'in-progress' || service.status === 'completed') {
      startTime = new Date(service.createdAt); // Assuming createdAt is when service started

      if (service.status === 'completed') {
        endTime = new Date(service.updatedAt);
        actualDuration = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60); // Convert to hours
        timeRemaining = 0;
        progressPercentage = 100;
      } else if (service.status === 'in-progress') {
        const now = new Date();
        actualDuration = (now.getTime() - startTime.getTime()) / (1000 * 60 * 60); // Convert to hours
        timeRemaining = Math.max(0, estimatedDuration - actualDuration);
        isOverdue = actualDuration > estimatedDuration;
        progressPercentage = Math.min(100, (actualDuration / estimatedDuration) * 100);
      }
    }

    return {
      serviceId: service._id,
      serviceName: service.serviceName,
      status: service.status,
      estimatedDuration,
      actualDuration,
      timeRemaining,
      isOverdue,
      startTime,
      endTime,
      progressPercentage,
    };
  }, []);

  const calculateOrderTime = useCallback((services: CorrectiveService[]): OrderTimeData => {
    const serviceTimeData = services.map(calculateServiceTime);

    const totalEstimatedTime = serviceTimeData.reduce((sum, data) => sum + data.estimatedDuration, 0);
    const totalActualTime = serviceTimeData.reduce((sum, data) => sum + data.actualDuration, 0);
    const totalTimeRemaining = serviceTimeData.reduce((sum, data) => sum + data.timeRemaining, 0);

    const completedServices = serviceTimeData.filter(data => data.status === 'completed').length;
    const inProgressServices = serviceTimeData.filter(data => data.status === 'in-progress').length;
    const overdueServices = serviceTimeData.filter(data => data.isOverdue).length;

    const overallProgress = services.length > 0 ? (completedServices / services.length) * 100 : 0;

    return {
      orderId,
      totalEstimatedTime,
      totalActualTime,
      totalTimeRemaining,
      overallProgress,
      completedServices,
      inProgressServices,
      overdueServices,
      services: serviceTimeData,
    };
  }, [orderId, calculateServiceTime]);

  // Update time data every minute when tracking is active
  useEffect(() => {
    const updateTimeData = () => {
      const newTimeData = calculateOrderTime(services);
      setTimeData(newTimeData);

      // Check if any service is in progress to determine if we should keep tracking
      const hasInProgressServices = newTimeData.inProgressServices > 0;
      setIsTracking(hasInProgressServices);
    };

    // Initial calculation
    updateTimeData();

    // Set up interval for real-time updates
    let interval: NodeJS.Timeout;
    if (isTracking || services.some(s => s.status === 'in-progress')) {
      interval = setInterval(updateTimeData, 60000); // Update every minute
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [services, calculateOrderTime, isTracking]);

  // Manual refresh function
  const refreshTimeData = useCallback(() => {
    const newTimeData = calculateOrderTime(services);
    setTimeData(newTimeData);
  }, [services, calculateOrderTime]);

  // Get time data for a specific service
  const getServiceTimeData = useCallback((serviceId: string): ServiceTimeData | undefined => {
    return timeData?.services.find(s => s.serviceId === serviceId);
  }, [timeData]);

  // Check if order is overdue
  const isOrderOverdue = useCallback((): boolean => {
    return timeData ? timeData.overdueServices > 0 : false;
  }, [timeData]);

  // Get estimated completion time
  const getEstimatedCompletionTime = useCallback((): Date | null => {
    if (!timeData || timeData.totalTimeRemaining <= 0) return null;

    const now = new Date();
    const completionTime = new Date(now.getTime() + (timeData.totalTimeRemaining * 60 * 60 * 1000));
    return completionTime;
  }, [timeData]);

  // Get SLA status
  const getSLAStatus = useCallback((): 'on-track' | 'at-risk' | 'overdue' => {
    if (!timeData) return 'on-track';

    if (timeData.overdueServices > 0) return 'overdue';

    // Consider at-risk if any service has less than 25% time remaining
    const atRiskServices = timeData.services.filter(s =>
      s.status === 'in-progress' &&
      s.timeRemaining > 0 &&
      (s.timeRemaining / s.estimatedDuration) < 0.25
    );

    if (atRiskServices.length > 0) return 'at-risk';

    return 'on-track';
  }, [timeData]);

  // Format duration helper
  const formatDuration = useCallback((hours: number): string => {
    if (hours < 1) {
      return `${Math.round(hours * 60)}m`;
    } else if (hours < 24) {
      const h = Math.floor(hours);
      const m = Math.round((hours - h) * 60);
      return m > 0 ? `${h}h ${m}m` : `${h}h`;
    } else {
      const d = Math.floor(hours / 24);
      const h = Math.round(hours % 24);
      return h > 0 ? `${d}d ${h}h` : `${d}d`;
    }
  }, []);

  return {
    timeData,
    isTracking,
    refreshTimeData,
    getServiceTimeData,
    isOrderOverdue,
    getEstimatedCompletionTime,
    getSLAStatus,
    formatDuration,
  };
}

// Hook for tracking multiple orders
export function useMultiOrderTimeTracking(orders: { _id: string; services?: CorrectiveService[] }[]) {
  const [ordersTimeData, setOrdersTimeData] = useState<Record<string, OrderTimeData>>({});

  useEffect(() => {
    const newOrdersTimeData: Record<string, OrderTimeData> = {};

    orders.forEach(order => {
      if (order.services && order.services.length > 0) {
        const services = order.services;
        const serviceTimeData = services.map(service => {
          const estimatedDuration = service.estimatedDuration;
          let actualDuration = 0;
          let timeRemaining = estimatedDuration;
          let isOverdue = false;
          let progressPercentage = 0;

          if (service.status === 'in-progress' || service.status === 'completed') {
            const startTime = new Date(service.createdAt);

            if (service.status === 'completed') {
              const endTime = new Date(service.updatedAt);
              actualDuration = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
              timeRemaining = 0;
              progressPercentage = 100;
            } else if (service.status === 'in-progress') {
              const now = new Date();
              actualDuration = (now.getTime() - startTime.getTime()) / (1000 * 60 * 60);
              timeRemaining = Math.max(0, estimatedDuration - actualDuration);
              isOverdue = actualDuration > estimatedDuration;
              progressPercentage = Math.min(100, (actualDuration / estimatedDuration) * 100);
            }
          }

          return {
            serviceId: service._id,
            serviceName: service.serviceName,
            status: service.status,
            estimatedDuration,
            actualDuration,
            timeRemaining,
            isOverdue,
            startTime: service.status !== 'not-started' ? new Date(service.createdAt) : undefined,
            endTime: service.status === 'completed' ? new Date(service.updatedAt) : undefined,
            progressPercentage,
          };
        });

        const totalEstimatedTime = serviceTimeData.reduce((sum, data) => sum + data.estimatedDuration, 0);
        const totalActualTime = serviceTimeData.reduce((sum, data) => sum + data.actualDuration, 0);
        const totalTimeRemaining = serviceTimeData.reduce((sum, data) => sum + data.timeRemaining, 0);

        const completedServices = serviceTimeData.filter(data => data.status === 'completed').length;
        const inProgressServices = serviceTimeData.filter(data => data.status === 'in-progress').length;
        const overdueServices = serviceTimeData.filter(data => data.isOverdue).length;

        const overallProgress = services.length > 0 ? (completedServices / services.length) * 100 : 0;

        newOrdersTimeData[order._id] = {
          orderId: order._id,
          totalEstimatedTime,
          totalActualTime,
          totalTimeRemaining,
          overallProgress,
          completedServices,
          inProgressServices,
          overdueServices,
          services: serviceTimeData,
        };
      }
    });

    setOrdersTimeData(newOrdersTimeData);
  }, [orders]);

  return {
    ordersTimeData,
    getOrderTimeData: (orderId: string) => ordersTimeData[orderId],
    getAllOverdueOrders: () => Object.values(ordersTimeData).filter(data => data.overdueServices > 0),
    getTotalActiveOrders: () => Object.values(ordersTimeData).filter(data => data.inProgressServices > 0).length,
  };
}
