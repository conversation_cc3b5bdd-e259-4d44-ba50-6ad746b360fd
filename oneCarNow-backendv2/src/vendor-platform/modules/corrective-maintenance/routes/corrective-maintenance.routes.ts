import { Router } from 'express';
import { errorHandlerV2 } from '@/clean/errors/errorHandler';
import { verifyTokenVendorPlatform } from '@/vendor-platform/middlewares/verifycation-token';
import { upload } from '@/multer/multer';
import {
  createCorrectiveMaintenanceOrder,
  completeDiagnosis,
  createQuotation,
  submitQuotationForApproval,
  processApprovalDecisions,
  getCorrectiveMaintenanceOrders,
  getQuotations,
  getCorrectiveMaintenanceOrderById,
  startWork,
  updateServiceProgress,
  completeOrder,
  createCorrectiveMaintenanceAppointment,
  debugCorrectiveMaintenanceOrders,
  getVehicleMaintenanceHistory,
  testEndpoint,
} from '../controllers/corrective-maintenance.controller';
import {
  checkPartsAvailability,
  searchInventoryParts,
  getInventorySummary,
  reserveParts,
  releaseReservedParts,
  consumeParts,
  canStartService,
} from '../controllers/inventory.controller';
import {
  startServiceTracking,
  updateServicePhase,
  pauseService,
  resumeService,
  completeServiceWithEvidence,
  getOrderProgress,
  getServiceDetails,
  getActiveServicesDashboard,
} from '../controllers/real-time-tracking.controller';

const correctiveMaintenanceRouter = Router();

// Base URL for corrective maintenance
const baseURL = '/corrective-maintenance';

// Test endpoint (put first)
correctiveMaintenanceRouter.get(`${baseURL}/test`, verifyTokenVendorPlatform, errorHandlerV2(testEndpoint));

// Debug Routes (put first to avoid conflicts)
correctiveMaintenanceRouter.get(
  `${baseURL}/debug`,
  verifyTokenVendorPlatform,
  errorHandlerV2(debugCorrectiveMaintenanceOrders)
);

// Vehicle maintenance history (put before generic routes)
correctiveMaintenanceRouter.get(
  `${baseURL}/vehicles/:vehicleId/history`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getVehicleMaintenanceHistory)
);

// Corrective Maintenance Order Routes
correctiveMaintenanceRouter.post(
  `${baseURL}/orders`,
  verifyTokenVendorPlatform,
  errorHandlerV2(createCorrectiveMaintenanceOrder)
);

correctiveMaintenanceRouter.get(
  `${baseURL}/orders`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getCorrectiveMaintenanceOrders)
);

correctiveMaintenanceRouter.get(
  `${baseURL}/orders/:orderId`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getCorrectiveMaintenanceOrderById)
);

// Diagnosis Routes
correctiveMaintenanceRouter.post(
  `${baseURL}/orders/:orderId/diagnosis`,
  verifyTokenVendorPlatform,
  upload.any(), // Accept any file fields (service_0_evidence, service_1_evidence, etc.)
  errorHandlerV2(completeDiagnosis)
);

// Quotation Routes
correctiveMaintenanceRouter.post(
  `${baseURL}/orders/:orderId/quotation`,
  verifyTokenVendorPlatform,
  errorHandlerV2(createQuotation)
);

correctiveMaintenanceRouter.get(
  `${baseURL}/quotations`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getQuotations)
);

correctiveMaintenanceRouter.post(
  `${baseURL}/quotations/:quotationId/submit`,
  verifyTokenVendorPlatform,
  errorHandlerV2(submitQuotationForApproval)
);

// Approval Routes
correctiveMaintenanceRouter.post(
  `${baseURL}/quotations/:quotationId/approve`,
  verifyTokenVendorPlatform,
  errorHandlerV2(processApprovalDecisions)
);

// Service Execution Routes
correctiveMaintenanceRouter.post(
  `${baseURL}/orders/:orderId/start`,
  verifyTokenVendorPlatform,
  errorHandlerV2(startWork)
);

correctiveMaintenanceRouter.patch(
  `${baseURL}/services/:serviceId/progress`,
  verifyTokenVendorPlatform,
  upload.fields([
    { name: 'progressPhotos', maxCount: 5 },
    { name: 'evidence', maxCount: 5 },
  ]),
  errorHandlerV2(updateServiceProgress)
);

correctiveMaintenanceRouter.post(
  `${baseURL}/orders/:orderId/complete`,
  verifyTokenVendorPlatform,
  upload.array('afterPhotos', 10),
  errorHandlerV2(completeOrder)
);

// Appointment Routes
correctiveMaintenanceRouter.post(
  `${baseURL}/orders/:orderId/appointment`,
  verifyTokenVendorPlatform,
  errorHandlerV2(createCorrectiveMaintenanceAppointment)
);

// Inventory Routes
correctiveMaintenanceRouter.post(
  `${baseURL}/inventory/check-availability`,
  verifyTokenVendorPlatform,
  errorHandlerV2(checkPartsAvailability)
);

correctiveMaintenanceRouter.get(
  `${baseURL}/inventory/search`,
  verifyTokenVendorPlatform,
  errorHandlerV2(searchInventoryParts)
);

correctiveMaintenanceRouter.get(
  `${baseURL}/inventory/summary`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getInventorySummary)
);

correctiveMaintenanceRouter.post(
  `${baseURL}/orders/:orderId/reserve-parts`,
  verifyTokenVendorPlatform,
  errorHandlerV2(reserveParts)
);

correctiveMaintenanceRouter.post(
  `${baseURL}/inventory/release-parts`,
  verifyTokenVendorPlatform,
  errorHandlerV2(releaseReservedParts)
);

correctiveMaintenanceRouter.post(
  `${baseURL}/inventory/consume-parts`,
  verifyTokenVendorPlatform,
  errorHandlerV2(consumeParts)
);

correctiveMaintenanceRouter.get(
  `${baseURL}/services/:serviceId/can-start`,
  verifyTokenVendorPlatform,
  errorHandlerV2(canStartService)
);

// Real-time Tracking Routes
correctiveMaintenanceRouter.post(
  `${baseURL}/services/:serviceId/start-tracking`,
  verifyTokenVendorPlatform,
  errorHandlerV2(startServiceTracking)
);

correctiveMaintenanceRouter.patch(
  `${baseURL}/services/:serviceId/phase`,
  verifyTokenVendorPlatform,
  upload.array('evidence', 10),
  errorHandlerV2(updateServicePhase)
);

correctiveMaintenanceRouter.post(
  `${baseURL}/services/:serviceId/pause`,
  verifyTokenVendorPlatform,
  errorHandlerV2(pauseService)
);

correctiveMaintenanceRouter.post(
  `${baseURL}/services/:serviceId/resume`,
  verifyTokenVendorPlatform,
  errorHandlerV2(resumeService)
);

correctiveMaintenanceRouter.post(
  `${baseURL}/services/:serviceId/complete-with-evidence`,
  verifyTokenVendorPlatform,
  upload.fields([
    { name: 'completionPhotos', maxCount: 10 },
    { name: 'completionVideos', maxCount: 5 },
    { name: 'qualityCheckPhotos', maxCount: 5 },
  ]),
  errorHandlerV2(completeServiceWithEvidence)
);

correctiveMaintenanceRouter.get(
  `${baseURL}/orders/:orderId/progress`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getOrderProgress)
);

correctiveMaintenanceRouter.get(
  `${baseURL}/services/:serviceId/details`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getServiceDetails)
);

correctiveMaintenanceRouter.get(
  `${baseURL}/dashboard/active-services`,
  verifyTokenVendorPlatform,
  errorHandlerV2(getActiveServicesDashboard)
);

export default correctiveMaintenanceRouter;
