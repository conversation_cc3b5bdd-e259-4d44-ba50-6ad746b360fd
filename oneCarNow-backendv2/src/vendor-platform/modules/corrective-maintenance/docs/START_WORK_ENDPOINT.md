# Start Work Endpoint Documentation

## Overview
The `startWork` endpoint allows workshops to begin work on approved corrective maintenance services. This endpoint verifies that ALL required parts are available before allowing work to start. If any parts are unavailable, the request will be rejected. Only when all parts are confirmed available will the services be set to `NOT_STARTED` status, ready for manual initiation by technicians.

## Endpoint Details

**URL**: `POST /vendor-platform/corrective-maintenance/orders/:orderId/start`

**Authentication**: Required (Vendor Platform Token)

**Parameters**:
- `orderId` (path parameter): The ID of the corrective maintenance order

## Request

### Headers
```
Authorization: Bearer <vendor_platform_token>
Content-Type: application/json
```

### Body
```json
{}
```
*No request body required*

## Response

### Success Response (200)
```json
{
  "message": "Work started successfully",
  "data": {
    "_id": "683cf8b7545e6ca21d70276a",
    "status": "in-progress",
    "startDate": "2024-01-15T10:30:00.000Z",
    "stockId": "...",
    "associateId": "...",
    "organizationId": "...",
    "workshopId": "...",
    "services": [
      {
        "_id": "service_id_1",
        "serviceName": "Frenos",
        "status": "not-started",
        "isApproved": true,
        "allPartsAvailable": true
      },
      {
        "_id": "service_id_2",
        "serviceName": "Llantas",
        "status": "waiting-for-parts",
        "isApproved": true,
        "allPartsAvailable": false
      }
    ],
    "workshop": {
      "name": "Taller Central",
      "location": "..."
    },
    "stockVehicle": {
      "brand": "Toyota",
      "model": "Corolla",
      "year": 2020,
      "plate": "ABC-123"
    },
    "associate": {
      "name": "Juan Pérez",
      "email": "<EMAIL>",
      "phone": "+521234567890"
    }
  }
}
```

### Error Responses

#### 400 - Bad Request (Invalid Status)
```json
{
  "message": "Cannot start work on order with status: pending. Order must be approved first.",
  "error": "..."
}
```

#### 400 - Bad Request (Parts Not Available)
```json
{
  "message": "No se puede iniciar el trabajo. Las siguientes refacciones no están disponibles: Frenos: Pastillas de freno (Necesita ser ordenada), Filtro de aceite (No disponible en inventario). Por favor, asegúrese de que todas las refacciones estén disponibles antes de iniciar el trabajo.",
  "error": "..."
}
```

#### 404 - Not Found
```json
{
  "message": "Corrective maintenance order not found",
  "error": "..."
}
```

#### 401 - Unauthorized
```json
{
  "message": "No autorizado",
  "data": "v"
}
```

## Business Logic

### Prerequisites
1. Order must exist and belong to the requesting organization
2. Order status must be `APPROVED`
3. Must have an approved quotation with at least one approved service

### Process Flow
1. **Validation**: Verify order exists, belongs to organization, and is in `APPROVED` status
2. **Quotation Check**: Find approved quotation and extract approved services
3. **Parts Availability Check**: Verify ALL parts are available for ALL services
   - If ANY part is unavailable → **REJECT REQUEST** with detailed error message
   - If ALL parts are available → Continue to next step
4. **Service Updates**: Update approved services to `not-started` status (ready to be manually started)
5. **Parts Reservation**: Reserve all required parts in inventory
6. **Status Management**: Order status remains `approved` (ready for work)
7. **Timestamps**: Set order `startDate`
8. **History Update**: Update vehicle maintenance history
9. **Notifications**: Send work started notifications

### Status Transitions

#### Order Status
- `APPROVED` → `APPROVED` (all parts available, services ready to be manually started)
- `APPROVED` → `ERROR` (if any parts are unavailable - work cannot start)

#### Service Status
- `NOT_STARTED` → `NOT_STARTED` (all parts available, ready to be manually started by technicians)
- If any parts are unavailable → Request rejected, no status changes

## Example Usage

### cURL
```bash
curl -X POST \
  http://localhost:3000/vendor-platform/corrective-maintenance/orders/683cf8b7545e6ca21d70276a/start \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### JavaScript
```javascript
const response = await fetch('/vendor-platform/corrective-maintenance/orders/683cf8b7545e6ca21d70276a/start', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({})
});

const result = await response.json();
console.log(result);
```

## Related Endpoints

- `GET /vendor-platform/corrective-maintenance/orders/:orderId` - Get order details
- `POST /vendor-platform/corrective-maintenance/quotations/:quotationId/approve` - Approve quotation
- `PATCH /vendor-platform/corrective-maintenance/services/:serviceId/progress` - Update service progress
- `POST /vendor-platform/corrective-maintenance/orders/:orderId/complete` - Complete order

## Notes

- Only approved services will be started
- Services waiting for parts will be automatically marked as `waiting-for-parts`
- Notifications are sent to fleet and customer when work starts
- Vehicle maintenance history is automatically updated
- SLA tracking begins when work starts
