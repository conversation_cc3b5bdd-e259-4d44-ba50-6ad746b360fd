'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { CompleteDiagnosisRequest, ApiResponse, Diagnosis } from '../types';

export async function completeDiagnosis(
  orderId: string,
  diagnosisData: CompleteDiagnosisRequest
): Promise<ApiResponse<Diagnosis>> {
  const user = await getCurrentUser();

  if (!user) {
    return {
      success: false,
      data: null as any,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔗 Completing diagnosis for order:', orderId, diagnosisData);

    // Use multipart/form-data as specified in the API documentation
    const formData = new FormData();
    formData.append('diagnosisNotes', diagnosisData.diagnosisNotes);

    // Prepare services data without evidence files for JSON
    const servicesForJson = diagnosisData.services.map(service => ({
      serviceType: service.serviceType,
      serviceName: service.serviceName,
      description: service.description,
      estimatedCost: service.estimatedCost,
      estimatedDuration: service.estimatedDuration,
      parts: service.parts
    }));

    formData.append('services', JSON.stringify(servicesForJson));

    // Append evidence files for each service
    diagnosisData.services.forEach((service, serviceIndex) => {
      if (service.evidence && service.evidence.length > 0) {
        service.evidence.forEach((file) => {
          formData.append(`service_${serviceIndex}_evidence`, file);
        });
      }
    });

    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/orders/${orderId}/diagnosis`,
      formData,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    console.log('✅ Diagnosis completed successfully:', response.data);

    // Transform API response to match our expected format
    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || apiResponse,
      message: apiResponse.message || 'Diagnóstico completado exitosamente',
    };
  } catch (error: any) {
    console.error('❌ Error completing diagnosis:', error.response?.data || error);

    return {
      success: false,
      data: null as any,
      message: error.response?.data?.message || 'Error al completar el diagnóstico',
    };
  }
}
