/* eslint-disable max-params */
import { Types } from 'mongoose';
import {
  InventoryPart,
  IInventoryPart,
  PartAvailabilityStatus,
  PartCategory,
} from '../models/inventory.model';
import { IPart } from '../models/corrective-service.model';
// import { HttpException } from '@/vendor-platform/exceptions/HttpExceptions';

export interface PartAvailabilityCheck {
  partNumber?: string;
  name: string;
  quantity: number;
  isAvailable: boolean;
  availableQuantity: number;
  estimatedArrival?: Date;
  alternativeParts?: IInventoryPart[];
  needsOrdering: boolean;
  supplier?: {
    name: string;
    leadTime: number;
    minimumOrderQuantity: number;
  };
}

export interface VehicleCompatibility {
  brand: string;
  model: string;
  year: number;
  version?: string;
}

export class InventoryService {
  /**
   * Check availability of multiple parts for a service
   */
  static async checkPartsAvailability(
    parts: IPart[],
    organizationId: string,
    vehicleInfo?: VehicleCompatibility
  ): Promise<PartAvailabilityCheck[]> {
    const results: PartAvailabilityCheck[] = [];

    for (const part of parts) {
      const availability = await this.checkSinglePartAvailability(part, organizationId, vehicleInfo);
      results.push(availability);
    }

    return results;
  }

  /**
   * Check availability of a single part
   */
  static async checkSinglePartAvailability(
    part: IPart,
    organizationId: string,
    vehicleInfo?: VehicleCompatibility
  ): Promise<PartAvailabilityCheck> {
    let inventoryPart: IInventoryPart | null = null;

    // First try to find by part number if provided
    if (part.partNumber) {
      inventoryPart = await InventoryPart.findOne({
        organizationId: new Types.ObjectId(organizationId),
        partNumber: part.partNumber,
        isActive: true,
      });
    }

    // If not found by part number, try to find by name and compatibility
    if (!inventoryPart) {
      const query: any = {
        organizationId: new Types.ObjectId(organizationId),
        name: { $regex: part.name, $options: 'i' },
        isActive: true,
      };

      // Add vehicle compatibility filter if vehicle info is provided
      if (vehicleInfo) {
        query.compatibleVehicles = {
          $elemMatch: {
            brand: { $regex: vehicleInfo.brand, $options: 'i' },
            model: { $regex: vehicleInfo.model, $options: 'i' },
            years: vehicleInfo.year,
          },
        };
      }

      inventoryPart = await InventoryPart.findOne(query);
    }

    if (!inventoryPart) {
      // Part not found in inventory
      return {
        partNumber: part.partNumber,
        name: part.name,
        quantity: part.quantity,
        isAvailable: false,
        availableQuantity: 0,
        needsOrdering: true,
        alternativeParts: await this.findAlternativeParts(part, organizationId, vehicleInfo),
      };
    }

    const isAvailable = inventoryPart.availableStock >= part.quantity;
    const primarySupplier = inventoryPart.suppliers.find((s) => s.isPrimary) || inventoryPart.suppliers[0];

    return {
      partNumber: inventoryPart.partNumber,
      name: inventoryPart.name,
      quantity: part.quantity,
      isAvailable,
      availableQuantity: inventoryPart.availableStock,
      estimatedArrival: inventoryPart.nextExpectedDelivery,
      needsOrdering: !isAvailable,
      supplier: primarySupplier
        ? {
            name: primarySupplier.supplierName,
            leadTime: primarySupplier.leadTime,
            minimumOrderQuantity: primarySupplier.minimumOrderQuantity,
          }
        : undefined,
    };
  }

  /**
   * Find alternative parts for a given part
   */
  static async findAlternativeParts(
    part: IPart,
    organizationId: string,
    vehicleInfo?: VehicleCompatibility
  ): Promise<IInventoryPart[]> {
    const query: any = {
      organizationId: new Types.ObjectId(organizationId),
      name: { $regex: part.name.split(' ').join('|'), $options: 'i' },
      isActive: true,
      availableStock: { $gt: 0 },
    };

    if (vehicleInfo) {
      query.compatibleVehicles = {
        $elemMatch: {
          brand: { $regex: vehicleInfo.brand, $options: 'i' },
          model: { $regex: vehicleInfo.model, $options: 'i' },
          years: vehicleInfo.year,
        },
      };
    }

    return InventoryPart.find(query).limit(5);
  }

  /**
   * Reserve parts for a service
   */
  static async reserveParts(parts: IPart[], organizationId: string, orderId: string): Promise<void> {
    console.log('Reserving parts:', parts, 'for order:', orderId);
    for (const part of parts) {
      if (part.partNumber) {
        const inventoryPart = await InventoryPart.findOne({
          organizationId: new Types.ObjectId(organizationId),
          partNumber: part.partNumber,
          isActive: true,
        });

        if (inventoryPart && inventoryPart.availableStock >= part.quantity) {
          inventoryPart.reservedStock += part.quantity;
          await inventoryPart.save();
        }
      }
    }
  }

  /**
   * Release reserved parts (when service is cancelled or completed)
   */
  static async releaseReservedParts(parts: IPart[], organizationId: string): Promise<void> {
    for (const part of parts) {
      if (part.partNumber) {
        const inventoryPart = await InventoryPart.findOne({
          organizationId: new Types.ObjectId(organizationId),
          partNumber: part.partNumber,
          isActive: true,
        });

        if (inventoryPart) {
          inventoryPart.reservedStock = Math.max(0, inventoryPart.reservedStock - part.quantity);
          await inventoryPart.save();
        }
      }
    }
  }

  /**
   * Consume parts (when service is completed)
   */
  static async consumeParts(parts: IPart[], organizationId: string): Promise<void> {
    for (const part of parts) {
      if (part.partNumber) {
        const inventoryPart = await InventoryPart.findOne({
          organizationId: new Types.ObjectId(organizationId),
          partNumber: part.partNumber,
          isActive: true,
        });

        if (inventoryPart) {
          // Reduce both current stock and reserved stock
          inventoryPart.currentStock = Math.max(0, inventoryPart.currentStock - part.quantity);
          inventoryPart.reservedStock = Math.max(0, inventoryPart.reservedStock - part.quantity);
          await inventoryPart.save();
        }
      }
    }
  }

  /**
   * Get inventory summary for an organization
   */
  static async getInventorySummary(organizationId: string) {
    const pipeline = [
      { $match: { organizationId: new Types.ObjectId(organizationId), isActive: true } },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 },
          totalValue: { $sum: { $multiply: ['$currentStock', '$unitCost'] } },
        },
      },
    ];

    const summary = await InventoryPart.aggregate(pipeline);

    const totalParts = await InventoryPart.countDocuments({
      organizationId: new Types.ObjectId(organizationId),
      isActive: true,
    });

    const lowStockParts = await InventoryPart.countDocuments({
      organizationId: new Types.ObjectId(organizationId),
      isActive: true,
      status: { $in: [PartAvailabilityStatus.LOW_STOCK, PartAvailabilityStatus.OUT_OF_STOCK] },
    });

    return {
      totalParts,
      lowStockParts,
      statusBreakdown: summary,
    };
  }

  /**
   * Search parts in inventory
   */
  static async searchParts(
    organizationId: string,
    searchTerm: string,
    category?: PartCategory,
    vehicleInfo?: VehicleCompatibility
  ): Promise<IInventoryPart[]> {
    const query: any = {
      organizationId: new Types.ObjectId(organizationId),
      isActive: true,
      $or: [
        { name: { $regex: searchTerm, $options: 'i' } },
        { partNumber: { $regex: searchTerm, $options: 'i' } },
        { description: { $regex: searchTerm, $options: 'i' } },
      ],
    };

    if (category) {
      query.category = category;
    }

    if (vehicleInfo) {
      query.compatibleVehicles = {
        $elemMatch: {
          brand: { $regex: vehicleInfo.brand, $options: 'i' },
          model: { $regex: vehicleInfo.model, $options: 'i' },
          years: vehicleInfo.year,
        },
      };
    }

    return InventoryPart.find(query).limit(50);
  }
}
