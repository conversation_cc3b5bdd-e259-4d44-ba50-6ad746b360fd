import express from 'express';
import {
  createNote<PERSON><PERSON>roller,
  getNotesByUserController,
  getNoteByIdController,
  updateNoteByIdController,
  deleteNoteByIdController,
  getNotesByAdmissionRequestController,
} from '../controllers/requestNotes';
import { cleanAuthMiddleware } from '../middlewares/cleanAuth';

const requestNotesRouter = express.Router();

requestNotesRouter.use(cleanAuthMiddleware); // Protect all routes

requestNotesRouter.post('/', createNoteController);
requestNotesRouter.get('/', getNotesByUserController);
requestNotesRouter.get('/:id', getNoteByIdController);
requestNotesRouter.put('/:id', updateNoteByIdController);
requestNotesRouter.delete('/:id', deleteNoteByIdController);
requestNotesRouter.get('/admission-request/:admissionRequestId', getNotesByAdmissionRequestController);

export default requestNotesRouter;
