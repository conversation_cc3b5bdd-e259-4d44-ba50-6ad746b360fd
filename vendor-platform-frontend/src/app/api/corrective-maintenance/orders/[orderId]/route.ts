import { NextRequest, NextResponse } from 'next/server';
import { URL_API } from '@/constants';
import getCurrentUser from '@/actions/getCurrentUser';
import axios from 'axios';

export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'Usuario no autenticado' },
        { status: 401 }
      );
    }

    const { orderId } = params;
    const { searchParams } = new URL(request.url);
    const bypassCache = searchParams.get('t'); // Cache busting parameter

    console.log('🔗 API Route - Getting order by ID:', {
      orderId,
      bypassCache: !!bypassCache,
      timestamp: new Date().toISOString()
    });

    const headers: any = {
      Authorization: `Bearer ${user.accessToken}`,
      'Content-Type': 'application/json',
    };

    // Add cache-busting headers when bypassing cache
    if (bypassCache) {
      headers['Cache-Control'] = 'no-cache';
      headers['Pragma'] = 'no-cache';
    }

    console.log('📞 API Route - Calling backend:', `${URL_API}/vendor-platform/corrective-maintenance/orders/${orderId}`);

    // Forward the request to the backend API using axios
    const response = await axios.get(
      `${URL_API}/vendor-platform/corrective-maintenance/orders/${orderId}`,
      { headers }
    );

    console.log('📥 API Route - Backend response status:', response.status);

    const responseData = response.data;

    // Log detailed information about the order and services
    console.log('📦 API Route - Backend response received:', {
      orderId,
      orderStatus: responseData.data?.status,
      servicesCount: responseData.data?.services?.length || 0,
      timestamp: new Date().toISOString()
    });

    // Log detailed service information
    if (responseData.data?.services) {
      console.log('🔍 API Route - Services details:');
      responseData.data.services.forEach((service: any, index: number) => {
        console.log(`  Service ${index + 1}:`, {
          id: service._id,
          name: service.serviceName,
          status: service.status,
          evidenceCount: service.evidence?.length || 0,
          evidenceTypes: service.evidence?.map((e: any) => e.type) || [],
          evidenceUrls: service.evidence?.map((e: any) => e.url?.substring(0, 50) + '...') || []
        });
      });
    }

    // Log the raw evidence data for debugging
    console.log('🔍 API Route - Raw evidence data:', JSON.stringify(
      responseData.data?.services?.map((s: any) => ({
        serviceId: s._id,
        serviceName: s.serviceName,
        evidence: s.evidence
      })) || [],
      null,
      2
    ));

    return NextResponse.json({
      success: true,
      data: responseData.data || responseData,
      message: responseData.message || 'Orden obtenida exitosamente',
    });
  } catch (error: any) {
    console.error('❌ API Route - Error getting order:', {
      orderId: params.orderId,
      error: error.response?.data || error.message,
      status: error.response?.status,
      timestamp: new Date().toISOString()
    });

    return NextResponse.json(
      {
        success: false,
        message: error.response?.data?.message || error.message || 'Error inesperado al obtener la orden',
        data: null,
      },
      { status: error.response?.status || 500 }
    );
  }
}
