'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/components/ui/use-toast';
import {
  Slack, Clock, AlertTriangle, CheckCircle, Bell,
  ExternalLink, Copy, Send, Users, Calendar, Loader2
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';

interface FleetQuotation {
  id: string;
  quotationNumber: string;
  fleetName: string;
  fleetId: string;
  vehicleInfo: {
    brand: string;
    model: string;
    year: number;
    plate: string;
  };
  serviceName: string;
  totalCost: number;
  status: 'pending' | 'sent' | 'approved' | 'rejected' | 'expired';
  createdAt: string;
  sentAt?: string;
  dueDate?: string;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  contactPerson: {
    name: string;
    email: string;
    phone?: string;
  };
  slackNotificationSent: boolean;
  slackChannel?: string;
  quotationUrl: string;
}

interface SlackNotificationConfig {
  webhookUrl: string;
  defaultChannel: string;
  urgentChannel: string;
  enabled: boolean;
}

interface FleetQuotationSlackNotifierProps {
  quotations: FleetQuotation[];
  slackConfig: SlackNotificationConfig;
  onConfigUpdate: (config: SlackNotificationConfig) => void;
  onQuotationUpdate: (quotationId: string, updates: Partial<FleetQuotation>) => void;
}

export default function FleetQuotationSlackNotifier({
  quotations,
  slackConfig,
  onConfigUpdate,
  onQuotationUpdate
}: FleetQuotationSlackNotifierProps) {
  const { toast } = useToast();
  const [isSending, setIsSending] = useState<string | null>(null);
  const [notifications, setNotifications] = useState<string[]>([]);

  // Filter pending quotations
  const pendingQuotations = quotations.filter(q =>
    q.status === 'pending' && !q.slackNotificationSent
  );

  // Filter overdue quotations
  const overdueQuotations = quotations.filter(q =>
    q.status === 'pending' &&
    q.dueDate &&
    new Date(q.dueDate) < new Date()
  );

  // Auto-check for new quotations that need notifications
  useEffect(() => {
    if (!slackConfig.enabled) return;

    const checkAndSendNotifications = async () => {
      for (const quotation of pendingQuotations) {
        if (!quotation.slackNotificationSent) {
          await sendSlackNotification(quotation, 'new');
        }
      }

      for (const quotation of overdueQuotations) {
        const lastNotification = notifications.find(n => n.includes(quotation.id));
        if (!lastNotification) {
          await sendSlackNotification(quotation, 'overdue');
        }
      }
    };

    // Check every 5 minutes
    const interval = setInterval(checkAndSendNotifications, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [pendingQuotations, overdueQuotations, slackConfig.enabled]);

  const sendSlackNotification = async (
    quotation: FleetQuotation,
    type: 'new' | 'reminder' | 'overdue' | 'urgent'
  ) => {
    if (!slackConfig.enabled || !slackConfig.webhookUrl) {
      toast({
        title: 'Error',
        description: 'Slack no está configurado correctamente',
        variant: 'destructive',
      });
      return false;
    }

    setIsSending(quotation.id);

    try {
      const slackMessage = createSlackMessage(quotation, type);
      const channel = quotation.priority === 'urgent' ?
        slackConfig.urgentChannel : slackConfig.defaultChannel;

      const response = await fetch('/api/notifications/slack/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          webhook_url: slackConfig.webhookUrl,
          channel: channel,
          message: slackMessage,
          quotation_id: quotation.id,
          type: type,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send Slack notification');
      }

      const result = await response.json();

      if (result.success) {
        toast({
          title: 'Éxito',
          description: 'Notificación enviada a Slack correctamente',
        });

        // Update quotation status
        onQuotationUpdate(quotation.id, {
          slackNotificationSent: true,
          slackChannel: channel,
        });

        // Add to notifications log
        setNotifications(prev => [
          ...prev,
          `${quotation.id}-${type}-${new Date().toISOString()}`
        ]);

        return true;
      } else {
        throw new Error(result.error || 'Unknown error');
      }
    } catch (error) {
      console.error('Error sending Slack notification:', error);
      toast({
        title: 'Error',
        description: 'Error al enviar notificación a Slack',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsSending(null);
    }
  };

  const createSlackMessage = (quotation: FleetQuotation, type: string) => {
    const baseUrl = window.location.origin;
    const quotationLink = `${baseUrl}${quotation.quotationUrl}`;

    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat('es-MX', {
        style: 'currency',
        currency: 'MXN'
      }).format(amount);
    };

    const getStatusEmoji = (type: string) => {
      switch (type) {
        case 'new': return '📋';
        case 'reminder': return '⏰';
        case 'overdue': return '🚨';
        case 'urgent': return '❗';
        default: return '📄';
      }
    };

    const getColorForType = (type: string) => {
      switch (type) {
        case 'new': return '#36a3eb';
        case 'reminder': return '#ffce3d';
        case 'overdue': return '#ff6b6b';
        case 'urgent': return '#dc3545';
        default: return '#6c757d';
      }
    };

    const getTitle = (type: string) => {
      switch (type) {
        case 'new': return 'Nueva Cotización Pendiente';
        case 'reminder': return 'Recordatorio de Cotización';
        case 'overdue': return 'Cotización Vencida';
        case 'urgent': return 'Cotización Urgente';
        default: return 'Cotización Pendiente';
      }
    };

    return {
      attachments: [
        {
          color: getColorForType(type),
          title: `${getStatusEmoji(type)} ${getTitle(type)}`,
          title_link: quotationLink,
          fields: [
            {
              title: 'Flota',
              value: quotation.fleetName,
              short: true
            },
            {
              title: 'Número de Cotización',
              value: quotation.quotationNumber,
              short: true
            },
            {
              title: 'Vehículo',
              value: `${quotation.vehicleInfo.brand} ${quotation.vehicleInfo.model} ${quotation.vehicleInfo.year}`,
              short: true
            },
            {
              title: 'Placas',
              value: quotation.vehicleInfo.plate,
              short: true
            },
            {
              title: 'Servicio',
              value: quotation.serviceName,
              short: false
            },
            {
              title: 'Costo Total',
              value: formatCurrency(quotation.totalCost),
              short: true
            },
            {
              title: 'Prioridad',
              value: quotation.priority.toUpperCase(),
              short: true
            },
            {
              title: 'Contacto',
              value: `${quotation.contactPerson.name}\n📧 ${quotation.contactPerson.email}${
                quotation.contactPerson.phone ? `\n📞 ${quotation.contactPerson.phone}` : ''
              }`,
              short: false
            },
            {
              title: 'Creada',
              value: formatDistanceToNow(new Date(quotation.createdAt), {
                locale: es,
                addSuffix: true
              }),
              short: true
            },
            ...(quotation.dueDate ? [{
              title: 'Vence',
              value: formatDistanceToNow(new Date(quotation.dueDate), {
                locale: es,
                addSuffix: true
              }),
              short: true
            }] : [])
          ],
          actions: [
            {
              type: 'button',
              text: 'Ver Cotización',
              url: quotationLink,
              style: 'primary'
            },
            {
              type: 'button',
              text: 'Aprobar',
              url: `${quotationLink}?action=approve`,
              style: 'primary'
            },
            {
              type: 'button',
              text: 'Rechazar',
              url: `${quotationLink}?action=reject`,
              style: 'danger'
            }
          ],
          footer: 'OneCarNow Fleet Management',
          footer_icon: 'https://your-domain.com/favicon.ico',
          ts: Math.floor(new Date().getTime() / 1000)
        }
      ]
    };
  };

  const sendManualNotification = async (quotationId: string, type: 'reminder' | 'urgent') => {
    const quotation = quotations.find(q => q.id === quotationId);
    if (!quotation) return;

    await sendSlackNotification(quotation, type);
  };

  const copyQuotationLink = (quotation: FleetQuotation) => {
    const baseUrl = window.location.origin;
    const fullUrl = `${baseUrl}${quotation.quotationUrl}`;

    navigator.clipboard.writeText(fullUrl).then(() => {
      toast({
        title: 'Enlace copiado',
        description: 'El enlace de la cotización se ha copiado al portapapeles',
      });
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'sent': return 'bg-blue-100 text-blue-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'expired': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'normal': return 'bg-blue-100 text-blue-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'urgent': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'MXN'
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Configuration Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Slack className="h-5 w-5" />
            Configuración de Notificaciones Slack
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`w-3 h-3 rounded-full ${
                slackConfig.enabled ? 'bg-green-500' : 'bg-red-500'
              }`} />
              <span className="text-sm">
                Estado: {slackConfig.enabled ? 'Activo' : 'Inactivo'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                Canal: {slackConfig.defaultChannel || 'No configurado'}
              </Badge>
              <Badge variant="outline">
                Urgente: {slackConfig.urgentChannel || 'No configurado'}
              </Badge>
            </div>
          </div>

          {!slackConfig.enabled && (
            <Alert className="mt-3">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Las notificaciones de Slack están desactivadas. Configura el webhook y canales para activarlas.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Pending Quotations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Cotizaciones Pendientes ({pendingQuotations.length})</span>
            <div className="flex items-center gap-2">
              {overdueQuotations.length > 0 && (
                <Badge variant="destructive">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  {overdueQuotations.length} vencidas
                </Badge>
              )}
              <Badge variant="secondary">
                <Bell className="h-3 w-3 mr-1" />
                {pendingQuotations.filter(q => !q.slackNotificationSent).length} sin notificar
              </Badge>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {pendingQuotations.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
              <p>No hay cotizaciones pendientes</p>
            </div>
          ) : (
            <div className="space-y-4">
              {pendingQuotations.map(quotation => {
                const isOverdue = quotation.dueDate && new Date(quotation.dueDate) < new Date();
                const isSendingThis = isSending === quotation.id;

                return (
                  <div
                    key={quotation.id}
                    className={`p-4 border rounded-lg ${
                      isOverdue ? 'border-red-200 bg-red-50' : 'border-gray-200'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="font-medium">{quotation.quotationNumber}</h4>
                          <Badge className={getStatusColor(quotation.status)}>
                            {quotation.status}
                          </Badge>
                          <Badge className={getPriorityColor(quotation.priority)}>
                            {quotation.priority}
                          </Badge>
                          {quotation.slackNotificationSent && (
                            <Badge variant="outline" className="text-green-600">
                              <Slack className="h-3 w-3 mr-1" />
                              Notificado
                            </Badge>
                          )}
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <p className="text-gray-500">Flota:</p>
                            <p className="font-medium">{quotation.fleetName}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Vehículo:</p>
                            <p className="font-medium">
                              {quotation.vehicleInfo.brand} {quotation.vehicleInfo.model}
                            </p>
                          </div>
                          <div>
                            <p className="text-gray-500">Servicio:</p>
                            <p className="font-medium">{quotation.serviceName}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Costo:</p>
                            <p className="font-medium">{formatCurrency(quotation.totalCost)}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-4 mt-3 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            <span>
                              Creada {formatDistanceToNow(new Date(quotation.createdAt), {
                                locale: es,
                                addSuffix: true
                              })}
                            </span>
                          </div>
                          {quotation.dueDate && (
                            <div className={`flex items-center gap-1 ${
                              isOverdue ? 'text-red-600 font-medium' : ''
                            }`}>
                              <Clock className="h-4 w-4" />
                              <span>
                                Vence {formatDistanceToNow(new Date(quotation.dueDate), {
                                  locale: es,
                                  addSuffix: true
                                })}
                              </span>
                            </div>
                          )}
                        </div>

                        <div className="mt-3">
                          <p className="text-sm text-gray-600 mb-1">
                            <strong>Contacto:</strong> {quotation.contactPerson.name} - {quotation.contactPerson.email}
                          </p>
                        </div>
                      </div>

                      <div className="flex flex-col gap-2 ml-4">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyQuotationLink(quotation)}
                        >
                          <Copy className="h-4 w-4 mr-1" />
                          Copiar Link
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          asChild
                        >
                          <a
                            href={quotation.quotationUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <ExternalLink className="h-4 w-4 mr-1" />
                            Ver
                          </a>
                        </Button>

                        {slackConfig.enabled && (
                          <>
                            {!quotation.slackNotificationSent && (
                              <Button
                                size="sm"
                                onClick={() => sendSlackNotification(quotation, 'new')}
                                disabled={isSendingThis}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                {isSendingThis ? (
                                  <Loader2 className="h-4 w-4 animate-spin mr-1" />
                                ) : (
                                  <Send className="h-4 w-4 mr-1" />
                                )}
                                Notificar
                              </Button>
                            )}

                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => sendManualNotification(quotation.id, 'reminder')}
                              disabled={isSendingThis}
                            >
                              {isSendingThis ? (
                                <Loader2 className="h-4 w-4 animate-spin mr-1" />
                              ) : (
                                <Bell className="h-4 w-4 mr-1" />
                              )}
                              Recordar
                            </Button>

                            {quotation.priority !== 'urgent' && (
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => sendManualNotification(quotation.id, 'urgent')}
                                disabled={isSendingThis}
                              >
                                {isSendingThis ? (
                                  <Loader2 className="h-4 w-4 animate-spin mr-1" />
                                ) : (
                                  <AlertTriangle className="h-4 w-4 mr-1" />
                                )}
                                Urgente
                              </Button>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Estadísticas de Notificaciones</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{quotations.length}</p>
              <p className="text-sm text-gray-600">Total Cotizaciones</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">{pendingQuotations.length}</p>
              <p className="text-sm text-gray-600">Pendientes</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">{overdueQuotations.length}</p>
              <p className="text-sm text-gray-600">Vencidas</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">
                {quotations.filter(q => q.slackNotificationSent).length}
              </p>
              <p className="text-sm text-gray-600">Notificadas</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}