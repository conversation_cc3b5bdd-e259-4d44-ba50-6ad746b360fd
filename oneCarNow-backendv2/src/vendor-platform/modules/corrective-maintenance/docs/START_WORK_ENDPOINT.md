# Start Work Endpoint Documentation

## Overview
The `startWork` endpoint allows workshops to begin work on approved corrective maintenance services. This endpoint transitions the order from `APPROVED` status to `IN_PROGRESS` and updates individual services accordingly.

## Endpoint Details

**URL**: `POST /vendor-platform/corrective-maintenance/orders/:orderId/start`

**Authentication**: Required (Vendor Platform Token)

**Parameters**:
- `orderId` (path parameter): The ID of the corrective maintenance order

## Request

### Headers
```
Authorization: Bearer <vendor_platform_token>
Content-Type: application/json
```

### Body
```json
{}
```
*No request body required*

## Response

### Success Response (200)
```json
{
  "message": "Work started successfully",
  "data": {
    "_id": "683cf8b7545e6ca21d70276a",
    "status": "in-progress",
    "startDate": "2024-01-15T10:30:00.000Z",
    "stockId": "...",
    "associateId": "...",
    "organizationId": "...",
    "workshopId": "...",
    "services": [
      {
        "_id": "service_id_1",
        "serviceName": "Frenos",
        "status": "not-started",
        "isApproved": true,
        "allPartsAvailable": true
      },
      {
        "_id": "service_id_2",
        "serviceName": "Llantas",
        "status": "waiting-for-parts",
        "isApproved": true,
        "allPartsAvailable": false
      }
    ],
    "workshop": {
      "name": "Taller Central",
      "location": "..."
    },
    "stockVehicle": {
      "brand": "Toyota",
      "model": "Corolla",
      "year": 2020,
      "plate": "ABC-123"
    },
    "associate": {
      "name": "Juan Pérez",
      "email": "<EMAIL>",
      "phone": "+521234567890"
    }
  }
}
```

### Error Responses

#### 400 - Bad Request
```json
{
  "message": "Cannot start work on order with status: pending. Order must be approved first.",
  "error": "..."
}
```

#### 404 - Not Found
```json
{
  "message": "Corrective maintenance order not found",
  "error": "..."
}
```

#### 401 - Unauthorized
```json
{
  "message": "No autorizado",
  "data": "v"
}
```

## Business Logic

### Prerequisites
1. Order must exist and belong to the requesting organization
2. Order status must be `APPROVED`
3. Must have an approved quotation with at least one approved service

### Process Flow
1. **Validation**: Verify order exists, belongs to organization, and is in `APPROVED` status
2. **Quotation Check**: Find approved quotation and extract approved services
3. **Service Updates**: Update approved services to `not-started` status (ready to be manually started)
4. **Parts Check**: Verify parts availability for each service
5. **Status Management**:
   - Services with available parts → `not-started` (ready to be manually started)
   - Services without parts → `waiting-for-parts`
   - Order status → `approved` (ready for work) or `waiting-for-parts` (if all services waiting)
6. **Timestamps**: Set order `startDate`
7. **History Update**: Update vehicle maintenance history
8. **Notifications**: Send work started notifications

### Status Transitions

#### Order Status
- `APPROVED` → `APPROVED` (services ready to be manually started)
- `APPROVED` → `WAITING_FOR_PARTS` (if all services need parts)

#### Service Status
- `NOT_STARTED` → `NOT_STARTED` (ready to be manually started by technicians)
- `NOT_STARTED` → `WAITING_FOR_PARTS` (if parts not available)

## Example Usage

### cURL
```bash
curl -X POST \
  http://localhost:3000/vendor-platform/corrective-maintenance/orders/683cf8b7545e6ca21d70276a/start \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{}'
```

### JavaScript
```javascript
const response = await fetch('/vendor-platform/corrective-maintenance/orders/683cf8b7545e6ca21d70276a/start', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({})
});

const result = await response.json();
console.log(result);
```

## Related Endpoints

- `GET /vendor-platform/corrective-maintenance/orders/:orderId` - Get order details
- `POST /vendor-platform/corrective-maintenance/quotations/:quotationId/approve` - Approve quotation
- `PATCH /vendor-platform/corrective-maintenance/services/:serviceId/progress` - Update service progress
- `POST /vendor-platform/corrective-maintenance/orders/:orderId/complete` - Complete order

## Notes

- Only approved services will be started
- Services waiting for parts will be automatically marked as `waiting-for-parts`
- Notifications are sent to fleet and customer when work starts
- Vehicle maintenance history is automatically updated
- SLA tracking begins when work starts
