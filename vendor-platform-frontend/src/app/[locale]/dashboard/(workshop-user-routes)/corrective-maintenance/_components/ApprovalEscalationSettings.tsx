'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Settings, Clock, Bell, Users, Save } from 'lucide-react';

interface EscalationSettings {
  autoEscalateEnabled: boolean;
  defaultCustomerTimeoutHours: number;
  checkIntervalMinutes: number;
  sendReminderBeforeEscalation: boolean;
  reminderHoursBeforeDeadline: number;
  enableSlackNotifications: boolean;
  escalationNotificationChannel: string;
}

interface ApprovalEscalationSettingsProps {
  settings: EscalationSettings;
  onSettingsUpdate: (settings: EscalationSettings) => void;
}

export default function ApprovalEscalationSettings({
  settings,
  onSettingsUpdate
}: ApprovalEscalationSettingsProps) {
  const { toast } = useToast();
  const [localSettings, setLocalSettings] = useState<EscalationSettings>(settings);
  const [isSaving, setIsSaving] = useState(false);

  const handleSettingChange = (key: keyof EscalationSettings, value: any) => {
    setLocalSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Validate settings
      if (localSettings.defaultCustomerTimeoutHours < 1 || localSettings.defaultCustomerTimeoutHours > 168) {
        throw new Error('El tiempo límite debe estar entre 1 y 168 horas');
      }

      if (localSettings.checkIntervalMinutes < 1 || localSettings.checkIntervalMinutes > 60) {
        throw new Error('El intervalo de verificación debe estar entre 1 y 60 minutos');
      }

      if (localSettings.reminderHoursBeforeDeadline < 1 || localSettings.reminderHoursBeforeDeadline > 24) {
        throw new Error('El recordatorio debe enviarse entre 1 y 24 horas antes');
      }

      onSettingsUpdate(localSettings);

      toast({
        title: 'Configuración Guardada',
        description: 'Los ajustes de escalación han sido actualizados',
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Error al guardar la configuración',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const hasChanges = JSON.stringify(settings) !== JSON.stringify(localSettings);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-gray-600" />
          Configuración de Escalación de Aprobaciones
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Auto Escalation Toggle */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Label className="text-base font-medium">Escalación Automática</Label>
            <p className="text-sm text-gray-600">
              Escalar automáticamente a fleet cuando el cliente no responde
            </p>
          </div>
          <Checkbox
            checked={localSettings.autoEscalateEnabled}
            onCheckedChange={(checked) => handleSettingChange('autoEscalateEnabled', checked)}
          />
        </div>

        {/* Default Timeout */}
        <div className="space-y-2">
          <Label htmlFor="defaultTimeout" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Tiempo Límite por Defecto (horas)
          </Label>
          <Input
            id="defaultTimeout"
            type="number"
            min="1"
            max="168"
            value={localSettings.defaultCustomerTimeoutHours}
            onChange={(e) => handleSettingChange('defaultCustomerTimeoutHours', parseInt(e.target.value))}
            placeholder="24"
          />
          <p className="text-xs text-gray-500">
            Tiempo que el cliente tiene para aprobar antes de escalar (1-168 horas)
          </p>
        </div>

        {/* Check Interval */}
        <div className="space-y-2">
          <Label htmlFor="checkInterval" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Intervalo de Verificación (minutos)
          </Label>
          <Input
            id="checkInterval"
            type="number"
            min="1"
            max="60"
            value={localSettings.checkIntervalMinutes}
            onChange={(e) => handleSettingChange('checkIntervalMinutes', parseInt(e.target.value))}
            placeholder="5"
          />
          <p className="text-xs text-gray-500">
            Frecuencia con la que se verifica si hay cotizaciones para escalar (1-60 minutos)
          </p>
        </div>

        {/* Reminder Settings */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-base font-medium flex items-center gap-2">
                <Bell className="h-4 w-4" />
                Recordatorios Automáticos
              </Label>
              <p className="text-sm text-gray-600">
                Enviar recordatorio al cliente antes de escalar
              </p>
            </div>
            <Checkbox
              checked={localSettings.sendReminderBeforeEscalation}
              onCheckedChange={(checked) => handleSettingChange('sendReminderBeforeEscalation', checked)}
            />
          </div>

          {localSettings.sendReminderBeforeEscalation && (
            <div className="space-y-2 ml-6">
              <Label htmlFor="reminderTiming">
                Enviar recordatorio (horas antes del vencimiento)
              </Label>
              <Input
                id="reminderTiming"
                type="number"
                min="1"
                max="24"
                value={localSettings.reminderHoursBeforeDeadline}
                onChange={(e) => handleSettingChange('reminderHoursBeforeDeadline', parseInt(e.target.value))}
                placeholder="2"
              />
              <p className="text-xs text-gray-500">
                Cuántas horas antes del vencimiento enviar el recordatorio (1-24 horas)
              </p>
            </div>
          )}
        </div>

        {/* Slack Notifications */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-base font-medium flex items-center gap-2">
                <Users className="h-4 w-4" />
                Notificaciones Slack
              </Label>
              <p className="text-sm text-gray-600">
                Notificar escalaciones por Slack
              </p>
            </div>
            <Checkbox
              checked={localSettings.enableSlackNotifications}
              onCheckedChange={(checked) => handleSettingChange('enableSlackNotifications', checked)}
            />
          </div>

          {localSettings.enableSlackNotifications && (
            <div className="space-y-2 ml-6">
              <Label htmlFor="slackChannel">
                Canal de Notificaciones
              </Label>
              <Input
                id="slackChannel"
                value={localSettings.escalationNotificationChannel}
                onChange={(e) => handleSettingChange('escalationNotificationChannel', e.target.value)}
                placeholder="#escalaciones"
              />
              <p className="text-xs text-gray-500">
                Canal donde se enviarán las notificaciones de escalación
              </p>
            </div>
          )}
        </div>

        {/* Current Status */}
        <div className="space-y-3 pt-4 border-t">
          <Label className="text-base font-medium">Estado Actual</Label>
          <div className="flex flex-wrap gap-2">
            <Badge variant={localSettings.autoEscalateEnabled ? "default" : "outline"}>
              {localSettings.autoEscalateEnabled ? "Escalación Activa" : "Escalación Desactivada"}
            </Badge>
            <Badge variant="secondary">
              Timeout: {localSettings.defaultCustomerTimeoutHours}h
            </Badge>
            <Badge variant="secondary">
              Verificación: {localSettings.checkIntervalMinutes}m
            </Badge>
            {localSettings.sendReminderBeforeEscalation && (
              <Badge variant="secondary">
                Recordatorio: {localSettings.reminderHoursBeforeDeadline}h antes
              </Badge>
            )}
            {localSettings.enableSlackNotifications && (
              <Badge variant="secondary">
                Slack: {localSettings.escalationNotificationChannel || 'No configurado'}
              </Badge>
            )}
          </div>
        </div>

        {/* Save Button */}
        <div className="flex justify-end pt-4">
          <Button
            onClick={handleSave}
            disabled={!hasChanges || isSaving}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            {isSaving ? 'Guardando...' : 'Guardar Configuración'}
          </Button>
        </div>

        {hasChanges && (
          <p className="text-sm text-orange-600 text-center">
            Hay cambios sin guardar
          </p>
        )}
      </CardContent>
    </Card>
  );
}
