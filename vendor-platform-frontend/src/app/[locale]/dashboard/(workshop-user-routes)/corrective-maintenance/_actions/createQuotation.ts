'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { CreateQuotationRequest, ApiResponse, Quotation } from '../types';

export async function createQuotation(
  orderId: string,
  quotationData: CreateQuotationRequest
): Promise<ApiResponse<Quotation>> {
  const user = await getCurrentUser();

  if (!user) {
    return {
      success: false,
      data: null as any,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔗 Creating quotation for order:', orderId, quotationData);

    // Since evidence is now required, we always use FormData
    const formData = new FormData();

    // Add basic quotation data
    formData.append('quotationNumber', quotationData.quotationNumber);
    formData.append('approvalType', quotationData.approvalType);
    formData.append('paymentTerms', quotationData.paymentTerms);

    if (quotationData.customerNotes) {
      formData.append('customerNotes', quotationData.customerNotes);
    }

    // Add services data (without evidence)
    const servicesWithoutEvidence = quotationData.services.map(service => ({
      serviceId: service.serviceId,
      serviceName: service.serviceName,
      description: service.description,
      estimatedCost: service.estimatedCost,
      estimatedDuration: service.estimatedDuration,
    }));
    formData.append('services', JSON.stringify(servicesWithoutEvidence));

    // Add evidence files (now required for each service)
    quotationData.services.forEach((service) => {
      service.evidence.forEach((file) => {
        formData.append(`evidence_${service.serviceId}`, file);
      });
    });

    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/orders/${orderId}/quotation`,
      formData,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    console.log('✅ Quotation created successfully:', response.data);

    // Transform API response to match our expected format
    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || apiResponse,
      message: apiResponse.message || 'Cotización creada exitosamente',
    };
  } catch (error: any) {
    console.error('❌ Error creating quotation:', error.response?.data || error);

    return {
      success: false,
      data: null as any,
      message: error.response?.data?.message || 'Error al crear la cotización',
    };
  }
}
