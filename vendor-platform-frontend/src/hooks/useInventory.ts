import { useState, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface Part {
  name: string;
  partNumber?: string;
  quantity: number;
  unitCost: number;
  totalCost: number;
  supplier: string;
}

interface PartAvailability {
  partNumber?: string;
  name: string;
  quantity: number;
  isAvailable: boolean;
  availableQuantity: number;
  estimatedArrival?: string;
  needsOrdering: boolean;
  supplier?: {
    name: string;
    leadTime: number;
    minimumOrderQuantity: number;
  };
}

interface InventoryCheckResult {
  allPartsAvailable: boolean;
  partsAvailability: PartAvailability[];
  partsNeedingOrder: number;
  summary: {
    totalParts: number;
    availableParts: number;
    unavailableParts: number;
  };
}

interface VehicleInfo {
  brand: string;
  model: string;
  year: number;
}

interface InventoryPart {
  _id: string;
  partNumber: string;
  name: string;
  description?: string;
  category: string;
  brand?: string;
  currentStock: number;
  availableStock: number;
  minimumStock: number;
  unitCost: number;
  sellingPrice: number;
  status: 'available' | 'low-stock' | 'out-of-stock' | 'pending-order';
}

interface InventorySummary {
  totalParts: number;
  lowStockParts: number;
  statusBreakdown: {
    _id: string;
    count: number;
    totalValue: number;
  }[];
}

export const useInventory = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const checkPartsAvailability = useCallback(async (
    parts: Part[],
    vehicleInfo?: VehicleInfo
  ): Promise<InventoryCheckResult | null> => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/corrective-maintenance/inventory/check-availability', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          parts,
          vehicleInfo,
        }),
      });

      if (!response.ok) {
        throw new Error('Error checking parts availability');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error checking parts availability:', error);
      toast({
        title: 'Error',
        description: 'No se pudo verificar la disponibilidad de las partes',
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const searchInventoryParts = useCallback(async (
    searchTerm: string,
    category?: string,
    vehicleInfo?: VehicleInfo
  ): Promise<InventoryPart[]> => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        search: searchTerm,
      });

      if (category) {
        params.append('category', category);
      }

      if (vehicleInfo) {
        params.append('vehicleBrand', vehicleInfo.brand);
        params.append('vehicleModel', vehicleInfo.model);
        params.append('vehicleYear', vehicleInfo.year.toString());
      }

      const response = await fetch(`/api/corrective-maintenance/inventory/search?${params}`);

      if (!response.ok) {
        throw new Error('Error searching inventory parts');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error searching inventory parts:', error);
      toast({
        title: 'Error',
        description: 'No se pudo buscar en el inventario',
        variant: 'destructive',
      });
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const getInventorySummary = useCallback(async (): Promise<InventorySummary | null> => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/corrective-maintenance/inventory/summary');

      if (!response.ok) {
        throw new Error('Error getting inventory summary');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error getting inventory summary:', error);
      toast({
        title: 'Error',
        description: 'No se pudo obtener el resumen del inventario',
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const reserveParts = useCallback(async (
    orderId: string,
    parts: Part[]
  ): Promise<boolean> => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/corrective-maintenance/orders/${orderId}/reserve-parts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ parts }),
      });

      if (!response.ok) {
        throw new Error('Error reserving parts');
      }

      toast({
        title: 'Éxito',
        description: 'Partes reservadas correctamente',
      });

      return true;
    } catch (error) {
      console.error('Error reserving parts:', error);
      toast({
        title: 'Error',
        description: 'No se pudieron reservar las partes',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const releaseReservedParts = useCallback(async (parts: Part[]): Promise<boolean> => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/corrective-maintenance/inventory/release-parts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ parts }),
      });

      if (!response.ok) {
        throw new Error('Error releasing reserved parts');
      }

      toast({
        title: 'Éxito',
        description: 'Reservas de partes liberadas correctamente',
      });

      return true;
    } catch (error) {
      console.error('Error releasing reserved parts:', error);
      toast({
        title: 'Error',
        description: 'No se pudieron liberar las reservas de partes',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const consumeParts = useCallback(async (parts: Part[]): Promise<boolean> => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/corrective-maintenance/inventory/consume-parts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ parts }),
      });

      if (!response.ok) {
        throw new Error('Error consuming parts');
      }

      toast({
        title: 'Éxito',
        description: 'Partes consumidas correctamente',
      });

      return true;
    } catch (error) {
      console.error('Error consuming parts:', error);
      toast({
        title: 'Error',
        description: 'No se pudieron consumir las partes',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const canStartService = useCallback(async (
    serviceId: string,
    ignorePartsAvailability: boolean = false
  ): Promise<{ canStart: boolean; reason: string; missingParts: string[] } | null> => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (ignorePartsAvailability) {
        params.append('ignorePartsAvailability', 'true');
      }

      const response = await fetch(
        `/api/corrective-maintenance/services/${serviceId}/can-start?${params}`
      );

      if (!response.ok) {
        throw new Error('Error checking if service can start');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error checking if service can start:', error);
      toast({
        title: 'Error',
        description: 'No se pudo verificar si el servicio puede iniciarse',
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  return {
    isLoading,
    checkPartsAvailability,
    searchInventoryParts,
    getInventorySummary,
    reserveParts,
    releaseReservedParts,
    consumeParts,
    canStartService,
  };
};
