/* eslint-disable max-params */
import { Types } from 'mongoose';
import {
  CorrectiveService,
  ICorrectiveService,
  CorrectiveServiceStatus,
} from '../models/corrective-service.model';
import { CorrectiveMaintenanceOrder } from '../models/corrective-maintenance-order.model';
import { HttpException } from '@/vendor-platform/exceptions/HttpExceptions';
import { BucketNameEnum, uploadFileAndReturnUrl } from '@/aws/s3';
import { logger } from '@/clean/lib/logger';

export interface ServicePhase {
  phase: string;
  startTime: Date;
  estimatedDuration: number; // minutes
  description: string;
}

export interface TimeTrackingUpdate {
  serviceId: string;
  action: 'start' | 'pause' | 'resume' | 'complete' | 'phase-change';
  phase?: string;
  notes?: string;
  pauseReason?: string;
  evidence?: {
    photos?: string[];
    videos?: string[];
    notes?: string;
  };
}

export interface ServiceProgress {
  serviceId: string;
  serviceName: string;
  status: CorrectiveServiceStatus;
  currentPhase?: string;
  progress: number; // 0-100
  timeSpent: number; // minutes
  estimatedTimeRemaining: number; // minutes
  isPaused: boolean;
  pauseReason?: string;
  lastActivity: Date;
  phases: {
    name: string;
    completed: boolean;
    startTime?: Date;
    endTime?: Date;
    duration?: number;
  }[];
}

export class RealTimeTrackingService {
  /**
   * Start a service and begin time tracking
   */
  static async startService(
    serviceId: string,
    organizationId: string,
    technicianId?: string
  ): Promise<ICorrectiveService> {
    try {
      const service = await CorrectiveService.findById(serviceId);
      if (!service) {
        throw HttpException.NotFound('Service not found');
      }

      // Verify service belongs to organization
      const order = await CorrectiveMaintenanceOrder.findOne({
        _id: service.orderId,
        organizationId: new Types.ObjectId(organizationId),
      });

      if (!order) {
        throw HttpException.NotFound('Service does not belong to your organization');
      }

      const currentTime = new Date();

      // Update service with start tracking
      const updateData: any = {
        status: CorrectiveServiceStatus.IN_PROGRESS,
        actualStartTime: currentTime,
        currentPhase: 'preparation',
        phaseStartTime: currentTime,
        lastActivityTime: currentTime,
        isPaused: false,
        timeSpentMinutes: 0,
        pausedTime: 0,
      };

      // Calculate estimated completion time
      if (service.estimatedDuration) {
        const estimatedCompletion = new Date(
          currentTime.getTime() + service.estimatedDuration * 60 * 60 * 1000
        );
        updateData.estimatedCompletionTime = estimatedCompletion;
      }

      const updatedService = await CorrectiveService.findByIdAndUpdate(
        serviceId,
        { $set: updateData },
        { new: true }
      );

      logger.info('Service started with time tracking', {
        serviceId,
        orderId: order._id,
        technicianId,
        startTime: currentTime,
      });

      return updatedService!;
    } catch (error) {
      logger.error('Error starting service time tracking', { error, serviceId });
      throw error;
    }
  }

  /**
   * Update service phase and track progress
   */
  static async updateServicePhase(
    serviceId: string,
    organizationId: string,
    phase: string,
    evidence?: {
      photos?: Express.Multer.File[];
      videos?: Express.Multer.File[];
      notes?: string;
    },
    technicianId?: string
  ): Promise<ICorrectiveService> {
    try {
      const service = await CorrectiveService.findById(serviceId);
      if (!service) {
        throw HttpException.NotFound('Service not found');
      }

      // Verify service belongs to organization
      const order = await CorrectiveMaintenanceOrder.findOne({
        _id: service.orderId,
        organizationId: new Types.ObjectId(organizationId),
      });

      if (!order) {
        throw HttpException.NotFound('Service does not belong to your organization');
      }

      const currentTime = new Date();

      // Calculate time spent in previous phase
      let timeSpentInPhase = 0;
      if (service.phaseStartTime) {
        timeSpentInPhase = Math.round(
          (currentTime.getTime() - service.phaseStartTime.getTime()) / (1000 * 60)
        );
      }

      // Upload evidence files if provided
      let uploadedPhotos: string[] = [];
      let uploadedVideos: string[] = [];

      if (evidence?.photos && evidence.photos.length > 0) {
        const photoUploads = await Promise.all(
          evidence.photos.map((file) =>
            uploadFileAndReturnUrl(file, {
              route: `corrective-maintenance/${order._id}/services/${serviceId}/phases/${phase}/`,
              bucketName: BucketNameEnum.VENDOR_PLATFORM,
              isPublic: true,
            })
          )
        );
        uploadedPhotos = photoUploads.map((upload) => upload.url).filter((url): url is string => !!url);
      }

      if (evidence?.videos && evidence.videos.length > 0) {
        const videoUploads = await Promise.all(
          evidence.videos.map((file) =>
            uploadFileAndReturnUrl(file, {
              route: `corrective-maintenance/${order._id}/services/${serviceId}/phases/${phase}/`,
              bucketName: BucketNameEnum.VENDOR_PLATFORM,
              isPublic: true,
            })
          )
        );
        uploadedVideos = videoUploads.map((upload) => upload.url).filter((url): url is string => !!url);
      }

      // Create evidence entry for the phase
      const phaseEvidence = {
        phase: service.currentPhase || 'unknown',
        photos: uploadedPhotos,
        videos: uploadedVideos,
        notes: evidence?.notes || '',
        timestamp: currentTime,
        technician: technicianId,
      };

      // Update service
      const updateData: any = {
        currentPhase: phase,
        phaseStartTime: currentTime,
        lastActivityTime: currentTime,
        timeSpentMinutes: (service.timeSpentMinutes || 0) + timeSpentInPhase,
        $push: {
          evidenceByPhase: phaseEvidence,
        },
      };

      const updatedService = await CorrectiveService.findByIdAndUpdate(serviceId, updateData, { new: true });

      logger.info('Service phase updated', {
        serviceId,
        oldPhase: service.currentPhase,
        newPhase: phase,
        timeSpentInPhase,
        evidenceAdded: uploadedPhotos.length + uploadedVideos.length,
      });

      return updatedService!;
    } catch (error) {
      logger.error('Error updating service phase', { error, serviceId, phase });
      throw error;
    }
  }

  /**
   * Pause service and track pause time
   */
  static async pauseService(
    serviceId: string,
    organizationId: string,
    pauseReason: string
  ): Promise<ICorrectiveService> {
    try {
      const service = await CorrectiveService.findById(serviceId);
      if (!service) {
        throw HttpException.NotFound('Service not found');
      }

      if (service.isPaused) {
        throw HttpException.BadRequest('Service is already paused');
      }

      const currentTime = new Date();

      // Calculate time spent since last activity
      let timeSpentSinceLastActivity = 0;
      if (service.phaseStartTime) {
        timeSpentSinceLastActivity = Math.round(
          (currentTime.getTime() - service.phaseStartTime.getTime()) / (1000 * 60)
        );
      }

      const updateData = {
        isPaused: true,
        pauseReason,
        lastActivityTime: currentTime,
        timeSpentMinutes: (service.timeSpentMinutes || 0) + timeSpentSinceLastActivity,
        phaseStartTime: currentTime, // Reset for when resumed
      };

      const updatedService = await CorrectiveService.findByIdAndUpdate(
        serviceId,
        { $set: updateData },
        { new: true }
      );

      logger.info('Service paused', {
        serviceId,
        pauseReason,
        timeSpentBeforePause: timeSpentSinceLastActivity,
      });

      return updatedService!;
    } catch (error) {
      logger.error('Error pausing service', { error, serviceId });
      throw error;
    }
  }

  /**
   * Resume paused service
   */
  static async resumeService(serviceId: string): Promise<ICorrectiveService> {
    try {
      const service = await CorrectiveService.findById(serviceId);
      if (!service) {
        throw HttpException.NotFound('Service not found');
      }

      if (!service.isPaused) {
        throw HttpException.BadRequest('Service is not paused');
      }

      const currentTime = new Date();

      // Calculate pause duration
      let pauseDuration = 0;
      if (service.lastActivityTime) {
        pauseDuration = Math.round(
          (currentTime.getTime() - service.lastActivityTime.getTime()) / (1000 * 60)
        );
      }

      const updateData = {
        isPaused: false,
        pauseReason: undefined,
        lastActivityTime: currentTime,
        phaseStartTime: currentTime,
        pausedTime: (service.pausedTime || 0) + pauseDuration,
      };

      const updatedService = await CorrectiveService.findByIdAndUpdate(
        serviceId,
        { $set: updateData },
        { new: true }
      );

      logger.info('Service resumed', {
        serviceId,
        pauseDuration,
        totalPausedTime: (service.pausedTime || 0) + pauseDuration,
      });

      return updatedService!;
    } catch (error) {
      logger.error('Error resuming service', { error, serviceId });
      throw error;
    }
  }

  /**
   * Complete service with final evidence
   */
  static async completeService(
    serviceId: string,
    organizationId: string,
    completionData: {
      notes?: string;
      qualityCheckPassed?: boolean;
      actualCost?: number;
      evidence?: {
        photos?: Express.Multer.File[];
        videos?: Express.Multer.File[];
        qualityCheckPhotos?: Express.Multer.File[];
        notes?: string;
      };
    }
  ): Promise<ICorrectiveService> {
    try {
      const service = await CorrectiveService.findById(serviceId);
      if (!service) {
        throw HttpException.NotFound('Service not found');
      }

      // Verify service belongs to organization
      const order = await CorrectiveMaintenanceOrder.findOne({
        _id: service.orderId,
        organizationId: new Types.ObjectId(organizationId),
      });

      if (!order) {
        throw HttpException.NotFound('Service does not belong to your organization');
      }

      const currentTime = new Date();

      // Calculate final time spent
      let finalTimeSpent = service.timeSpentMinutes || 0;
      if (service.phaseStartTime && !service.isPaused) {
        const timeInCurrentPhase = Math.round(
          (currentTime.getTime() - service.phaseStartTime.getTime()) / (1000 * 60)
        );
        finalTimeSpent += timeInCurrentPhase;
      }

      // Upload completion evidence
      let completionEvidence: any = {};

      if (completionData.evidence) {
        if (completionData.evidence.photos && completionData.evidence.photos.length > 0) {
          const photoUploads = await Promise.all(
            completionData.evidence.photos.map((file) =>
              uploadFileAndReturnUrl(file, {
                route: `corrective-maintenance/${order._id}/services/${serviceId}/completion/`,
                bucketName: BucketNameEnum.VENDOR_PLATFORM,
                isPublic: true,
              })
            )
          );
          completionEvidence.photos = photoUploads
            .map((upload) => upload.url)
            .filter((url): url is string => !!url);
        }

        if (
          completionData.evidence.qualityCheckPhotos &&
          completionData.evidence.qualityCheckPhotos.length > 0
        ) {
          const qcPhotoUploads = await Promise.all(
            completionData.evidence.qualityCheckPhotos.map((file) =>
              uploadFileAndReturnUrl(file, {
                route: `corrective-maintenance/${order._id}/services/${serviceId}/quality-check/`,
                bucketName: BucketNameEnum.VENDOR_PLATFORM,
                isPublic: true,
              })
            )
          );
          completionEvidence.qualityCheckPhotos = qcPhotoUploads
            .map((upload) => upload.url)
            .filter((url): url is string => !!url);
        }

        if (completionData.evidence.notes) {
          completionEvidence.notes = completionData.evidence.notes;
        }
      }

      // Update service with completion data
      const updateData: any = {
        status: CorrectiveServiceStatus.COMPLETED,
        actualEndTime: currentTime,
        currentPhase: 'completed',
        lastActivityTime: currentTime,
        timeSpentMinutes: finalTimeSpent,
        actualDuration: Math.round(finalTimeSpent / 60), // Convert to hours
        isPaused: false,
        completionEvidence,
      };

      if (completionData.qualityCheckPassed !== undefined) {
        updateData.qualityCheckPassed = completionData.qualityCheckPassed;
      }

      if (completionData.actualCost !== undefined) {
        updateData.actualCost = completionData.actualCost;
      }

      if (completionData.notes) {
        updateData.$push = {
          technicalNotes: `${currentTime.toISOString()}: ${completionData.notes}`,
        };
      }

      const updatedService = await CorrectiveService.findByIdAndUpdate(serviceId, updateData, { new: true });

      logger.info('Service completed', {
        serviceId,
        orderId: order._id,
        finalTimeSpent,
        actualDuration: updateData.actualDuration,
        qualityCheckPassed: completionData.qualityCheckPassed,
      });

      return updatedService!;
    } catch (error) {
      logger.error('Error completing service', { error, serviceId });
      throw error;
    }
  }

  /**
   * Get real-time progress for all services in an order
   */
  static async getOrderProgress(orderId: string, organizationId: string): Promise<ServiceProgress[]> {
    try {
      // Verify order belongs to organization
      const order = await CorrectiveMaintenanceOrder.findOne({
        _id: orderId,
        organizationId: new Types.ObjectId(organizationId),
      });

      if (!order) {
        throw HttpException.NotFound('Order not found or does not belong to your organization');
      }

      const services = await CorrectiveService.find({ orderId: new Types.ObjectId(orderId) });

      const currentTime = new Date();

      return services.map((service) => {
        // Calculate current time spent
        let currentTimeSpent = service.timeSpentMinutes || 0;
        if (
          service.phaseStartTime &&
          !service.isPaused &&
          service.status === CorrectiveServiceStatus.IN_PROGRESS
        ) {
          const timeInCurrentPhase = Math.round(
            (currentTime.getTime() - service.phaseStartTime.getTime()) / (1000 * 60)
          );
          currentTimeSpent += timeInCurrentPhase;
        }

        // Calculate progress percentage
        const estimatedTotalMinutes = (service.estimatedDuration || 1) * 60;
        const progress = Math.min(100, Math.round((currentTimeSpent / estimatedTotalMinutes) * 100));

        // Calculate estimated time remaining
        const estimatedTimeRemaining = Math.max(0, estimatedTotalMinutes - currentTimeSpent);

        // Define standard phases
        const standardPhases = [
          { name: 'preparation', completed: false },
          { name: 'diagnosis', completed: false },
          { name: 'work-in-progress', completed: false },
          { name: 'quality-check', completed: false },
          { name: 'completed', completed: false },
        ];

        // Update phases based on current status
        const phases = standardPhases.map((phase) => {
          const isCurrentPhase = service.currentPhase === phase.name;
          const isCompleted =
            service.status === CorrectiveServiceStatus.COMPLETED ||
            (service.currentPhase &&
              standardPhases.findIndex((p) => p.name === service.currentPhase) >
                standardPhases.findIndex((p) => p.name === phase.name));

          return {
            ...phase,
            completed: Boolean(isCompleted),
            startTime: isCurrentPhase ? service.phaseStartTime : undefined,
            endTime: isCompleted && !isCurrentPhase ? service.phaseStartTime : undefined,
          };
        });

        return {
          serviceId: service._id.toString(),
          serviceName: service.serviceName,
          status: service.status,
          currentPhase: service.currentPhase,
          progress,
          timeSpent: currentTimeSpent,
          estimatedTimeRemaining,
          isPaused: service.isPaused || false,
          pauseReason: service.pauseReason,
          lastActivity: service.lastActivityTime || service.createdAt,
          phases,
        };
      });
    } catch (error) {
      logger.error('Error getting order progress', { error, orderId });
      throw error;
    }
  }
}
