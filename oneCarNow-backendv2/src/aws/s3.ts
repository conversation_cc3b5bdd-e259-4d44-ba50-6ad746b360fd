/* eslint-disable max-params */
import fs from 'fs';
import {
  AWS_BUCKET_NAME,
  AWS_BUCKET_PUBLIC_KEY,
  AWS_BUCKET_REGION,
  AWS_BUCKET_SECRET_KEY,
  isDev,
} from '../constants';
import {
  S3Client,
  PutObjectCommand,
  ListO<PERSON>sCommand,
  GetObjectCommand,
  DeleteObjectCommand,
  ListObjectsV2Command,
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import Associate from '../models/associateSchema';
import Document from '../models/documentSchema';
import { Readable } from 'stream';
import { removeEmptySpacesNameFile } from '@/services/removeEmptySpaces';
import { logger } from '@/clean/lib/logger';

const client = new S3Client({
  region: AWS_BUCKET_REGION,
  credentials: {
    accessKeyId: AWS_BUCKET_PUBLIC_KEY,
    secretAccessKey: AWS_BUCKET_SECRET_KEY,
  },
});

export const BUCKETS_NAME = {
  VENDOR_PLATFORM: 'vendorplatform',
  BACKEND: AWS_BUCKET_NAME,
} as const;

// export type TBucketName = keyof typeof BUCKETS_NAME;
export enum BucketNameEnum {
  VENDOR_PLATFORM = 'VENDOR_PLATFORM',
  BACKEND = 'BACKEND',
}

export async function uploadToS3(file: Express.Multer.File, route?: string) {
  if (!file) throw new Error('Missing: Please upload a file');
  const uploadParams = {
    Bucket: AWS_BUCKET_NAME,
    Key: route + file.originalname,
    Body: file.buffer,
    ContentType: file.mimetype,
    ContentLength: file.size,
  };
  await client.send(new PutObjectCommand(uploadParams));
  const expirationInSeconds = 3600;
  const getUrlParams = {
    Bucket: AWS_BUCKET_NAME,
    Key: route + file.originalname,
    Expires: Math.floor(Date.now() / 1000) + expirationInSeconds, // Calcula la marca de tiempo Unix para la expiración
  };

  return new GetObjectCommand(getUrlParams);
}

/**
 * Uploads a single file to AWS S3
 * @param {file} file type file
 * @param {nameFile} nameFile name of file
 * @param {route} route route or path inside S3 Bucket
 */

export async function uploadFile(file: Express.Multer.File, nameFile: string, route?: string) {
  if (!file) throw new Error('Se necesita un archivo para subir');

  const parts = nameFile.split('.');
  const extension = parts[parts.length - 1];
  const isPDF = extension === 'pdf';
  const isImage = ['png', 'jpg', 'jpeg', 'gif'].includes(extension.toLowerCase());
  const contentType = isPDF
    ? 'application/pdf'
    : isImage
      ? `image/${extension}`
      : file.mimetype || 'application/octet-stream';

  // Use Buffer directly when available instead of creating a stream
  // This avoids Transfer-Encoding chunked issues
  let body: Buffer | Readable;

  if (file.buffer) {
    // Use the buffer directly for in-memory files (like our QR codes)
    body = file.buffer;
  } else {
    // For files on disk, create a read stream
    body = fs.createReadStream(file.path);
  }

  const uploadParams = {
    Bucket: AWS_BUCKET_NAME,
    Key: route + nameFile,
    Body: body,
    ContentType: contentType,
    // Explicitly set ContentLength when we know the size (for buffers)
    ...(file.buffer && { ContentLength: file.size || file.buffer.length }),
  };

  const command = new PutObjectCommand(uploadParams);
  await client.send(command);

  const expirationInSeconds = 3600;
  const getUrlParams = {
    Bucket: AWS_BUCKET_NAME,
    Key: route + nameFile,
    Expires: Math.floor(Date.now() / 1000) + expirationInSeconds,
  };

  const command2 = new GetObjectCommand(getUrlParams);

  // Only try to delete local file if it exists on disk
  if (file.path) {
    fs.unlink(file.path, (err) => {
      if (err && !err.message.includes('ENOENT')) {
        logger.error(`Error deleting temporary file ${file.path}:`, err);
      }
    });
  }

  return command2;
}

export async function uploadFileReadable(readable: Readable, route?: string) {
  // eslint-disable-next-line @typescript-eslint/no-use-before-define
  const pdfBuffer = await streamToBuffer(readable);
  const uploadParams = {
    Bucket: AWS_BUCKET_NAME,
    Key: route,
    Body: pdfBuffer,
    ContentType: 'application/pdf',
  };

  const command = new PutObjectCommand(uploadParams);

  await client.send(command);

  const expirationInSeconds = 3600;

  const getUrlParams = {
    Bucket: AWS_BUCKET_NAME,
    Key: route,
    Expires: Math.floor(Date.now() / 1000) + expirationInSeconds, // Calcula la marca de tiempo Unix para la expiración
  };

  const command2 = new GetObjectCommand(getUrlParams);

  return command2;
}

// async function streamToBuffer(stream: Readable): Promise<Buffer> {
//   const chunks: Uint8Array[] = [];
//   for await (const chunk of stream) {
//     chunks.push(typeof chunk === 'string' ? Buffer.from(chunk) : chunk);
//   }
//   return Buffer.concat(chunks);
// }

export async function getFiles() {
  const command = new ListObjectsCommand({ Bucket: AWS_BUCKET_NAME });

  const result = await client.send(command);
  return result;
}

// OBTENER UN SOLO ARCHIVO

export async function getUrlSingleFile(
  fileName: string,
  bucketName: BucketNameEnum = BucketNameEnum.BACKEND
) {
  const getUrlParams = {
    // Bucket: bucketName,
    Bucket: BUCKETS_NAME[bucketName] || AWS_BUCKET_NAME,
    Key: fileName, // Ruta del archivo en el bucket
  };

  const command = new GetObjectCommand(getUrlParams);

  // Extender tiempo de expiración a 1 hora (3600 segundos)
  const url = await getSignedUrl(client, command, { expiresIn: 3600 });

  return url;
}

export async function getUrlFromArray(files: string[]) {
  const urls = [];
  for (const file of files) {
    const getUrlParams = {
      Bucket: AWS_BUCKET_NAME,
      Key: file, // Ruta del archivo en el bucket
    };

    const command = new GetObjectCommand(getUrlParams);

    const url = await getSignedUrl(client, command);
    urls.push(url);
  }

  return urls;
}
//Delete file

export async function deleteFileFromS3(
  filePath: string,
  bucketName: BucketNameEnum = BucketNameEnum.BACKEND
) {
  const deleteParams = {
    // Bucket: AWS_BUCKET_NAME,
    Bucket: BUCKETS_NAME[bucketName] || AWS_BUCKET_NAME,
    Key: filePath,
  };
  const deleteCommand = new DeleteObjectCommand(deleteParams);
  try {
    await client.send(deleteCommand);
  } catch (err) {
    logger.error(`Error al eliminar el archivo ${filePath} de S3:`, err);
    throw err; // Puedes manejar el error de acuerdo a tus necesidades.
  }
}

export async function streamToBuffer(stream: Readable): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    const chunks: Uint8Array[] = [];
    stream.on('data', (chunk: Uint8Array) => chunks.push(chunk));
    stream.on('end', () => resolve(Buffer.concat(chunks)));
    stream.on('error', reject);
  });
}

export async function fixAllDocsNotRelatedCorrectlyWithDB() {
  try {
    // Parámetros para listar los objetos en el bucket y prefijo deseado

    // Ejecuta el comando para listar objetos
    const dirs = [
      'contract',
      'promissoryNote',
      'warranty',
      'invoice',
      'deliveryReceipt',
      'privacy',
      'contactInfo',
    ];
    let isTruncated = true;
    const total: string[] = [];
    let marker = null;

    const limitChange = new Date('2024-01-12');
    let counter = 0;

    while (isTruncated) {
      if (counter > 1) {
        isTruncated = false;
        break;
      }
      let params: any = { Bucket: AWS_BUCKET_NAME, Prefix: 'associate/' };
      if (marker) {
        params.Marker = marker;
      }
      const res = await client.send(new ListObjectsCommand(params));

      if (res.IsTruncated === false) {
        isTruncated = false;
      } else {
        isTruncated = true;
      }

      if (res.Contents) {
        await Promise.all(
          res.Contents.map(async (item) => {
            if (item.Key) {
              const keyParts = item.Key.split('/');
              if (
                // keyParts[0] === 'associate' &&
                item.LastModified &&
                item.LastModified < limitChange &&
                // keyParts[2] === '<EMAIL>' &&
                dirs.includes(keyParts[3])
              ) {
                total.push(item.Key);

                const email = keyParts[2].trim();
                const associate = await Associate.findOne({ email });
                if (associate) {
                  const property = associate.signDocs?.[keyParts[3] as keyof typeof associate.signDocs];
                  const doc = await Document.findById(property);
                  if (doc) {
                    doc.path = item.Key;
                    doc.originalName = keyParts[4];
                    logger.info('[DOCUMENTO ACTUALIZADO]', doc.path);
                    logger.info('[S3 KEY]', item.Key);
                    await doc.save();
                  }
                }
              }
            }
          })
        );
      }

      logger.info('is truncated 2: ', res.IsTruncated, res.Contents?.length);

      if (isTruncated) {
        marker = res.Contents?.slice(-1)[0].Key;
      }
      counter++;
    }
    logger.info('total objects real : ' + total.length);
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    await getAndChangeContentType(total);

    logger.info('FINISHED!');

    return total;
  } catch (err) {
    logger.error('Error:', err);
    throw new Error('Error al obtener las keys de S3');
  }
}

export const getAndChangeContentType = async (keys: string[]) => {
  try {
    logger.info('LENGTH: ', keys.length);
    for (let key of keys) {
      const object = await client.send(new GetObjectCommand({ Bucket: AWS_BUCKET_NAME, Key: key }));
      // This change the content type of file to be application/pdf if doesn't have pdf as content type
      logger.info('CONTENT TYPE: ', object.ContentType, object.ContentType !== 'application/pdf');
      if (object.ContentType !== 'application/pdf') {
        const bodyBuffer = await streamToBuffer(object.Body as Readable);
        logger.info('KEY TO UPDATE: ', key);

        const putParams = {
          Bucket: AWS_BUCKET_NAME,
          Key: key,
          Body: bodyBuffer,
          ContentType: 'application/pdf',
        };
        const resClient = await client.send(new PutObjectCommand(putParams));
        logger.info('object updated', resClient.$metadata.httpStatusCode);
      }
    }
  } catch (error) {
    logger.error(error);
  }
};

export async function getSpecifiedBucketKeys() {
  try {
    logger.info('iniciando busqueda');
    let params: any = { Bucket: AWS_BUCKET_NAME, Prefix: 'associate/1041/<EMAIL>/ine/' };

    const res = await client.send(new ListObjectsCommand(params));

    logger.info(res.Contents);

    logger.info('FINISHED!');
  } catch (err) {
    logger.error('Error:', err);
    throw new Error('Error al obtener las keys de S3');
  }
}

export async function deleteFolderContentsRecursive(folderPath: string) {
  const listParams = {
    Bucket: AWS_BUCKET_NAME,
    Prefix: folderPath,
  };

  const listObjectsCommand = new ListObjectsV2Command(listParams);
  const { Contents } = await client.send(listObjectsCommand);

  // Eliminar cada objeto en la carpeta
  if (Contents) {
    await Promise.all(
      Contents.map(async (obj) => {
        if (obj?.Key?.endsWith('/')) {
          // Es una carpeta, llama recursivamente para eliminar su contenido
          await deleteFolderContentsRecursive(obj.Key);
        } else {
          // Es un objeto, elimínalo
          const deleteParams = {
            Bucket: AWS_BUCKET_NAME,
            Key: obj.Key,
          };
          const deleteCommand = new DeleteObjectCommand(deleteParams);
          await client.send(deleteCommand);
        }
      })
    );
  }

  // Eliminar la carpeta después de eliminar los objetos
  const deleteFolderParams = {
    Bucket: AWS_BUCKET_NAME,
    Key: folderPath,
  };

  const deleteFolderCommand = new DeleteObjectCommand(deleteFolderParams);
  await client.send(deleteFolderCommand);
}

export async function uploadFileAndReturnUrl(
  file: Express.Multer.File,
  {
    route,
    bucketName = BucketNameEnum.BACKEND,
    isPublic = true,
  }: { route: string; bucketName?: BucketNameEnum; isPublic?: boolean }
) {
  if (!file) throw new Error('Se necesita un archivo para subir');

  const stream = file.buffer ? Readable.from(file.buffer) : fs.createReadStream(file.path);

  // const nameFile
  const nameFile = removeEmptySpacesNameFile(file, 8);

  const parts = nameFile.split('.');

  const extension = parts[parts.length - 1];
  const isPDF = extension === 'pdf';

  const ContentType = isPDF ? 'application/pdf' : 'image/' + extension;

  // Public and private files route just apply to vendor platform bucket
  const prefixKey = isPublic ? 'public/' : 'private/';
  const prefix = isDev ? prefixKey + 'dev/' : prefixKey; // Prefix to save file depending on environment

  const removePrefixWhenIsNotVendorPlatform = bucketName === BucketNameEnum.BACKEND ? '' : prefix;

  const Key = removePrefixWhenIsNotVendorPlatform + route + '/' + nameFile;

  const uploadParams = {
    Bucket: BUCKETS_NAME[bucketName] || AWS_BUCKET_NAME,
    Key,
    Body: stream,
    ContentType,
  };

  try {
    const command = new PutObjectCommand(uploadParams);

    await client.send(command);

    const getUrlParams = {
      Bucket: BUCKETS_NAME[bucketName] || AWS_BUCKET_NAME,
      Key,
    };

    const command2 = new GetObjectCommand(getUrlParams);

    const url = await getSignedUrl(client, command2);

    const urlWithoutQuery = url.split('?')[0];

    const exists = fs.existsSync(file.path);

    if (exists) {
      fs.unlinkSync(file.path);
    }
    try {
      fs.unlink(file.path, (err) => {
        if (err) {
          // Manejar el error si ocurre al eliminar el archivo
        }
      });
    } catch (error) {
      logger.error('Error al eliminar el archivo:', error);
    }

    return {
      url: urlWithoutQuery,
      fullUrl: url,
      Key: command2.input.Key,
    };
  } catch (error) {
    const exists = fs.existsSync(file.path);

    if (exists) {
      fs.unlinkSync(file.path);
    }
    try {
      fs.unlink(file.path, (err) => {
        if (err) {
          // Manejar el error si ocurre al eliminar el archivo
        }
      });
    } catch (err) {
      logger.error('Error al eliminar el archivo:', err);
    }

    return {
      url: undefined,
      fullUrl: undefined,
      Key: undefined,
    };
  }
}
