import React from 'react';
import { notFound } from 'next/navigation';
import { getOrderById } from '../_actions/getOrders';
import OrderDetailClient from './client';

// Disable caching for this page to ensure fresh data
export const dynamic = 'force-dynamic';
export const revalidate = 0;

interface PageProps {
  params: {
    orderId: string;
  };
}

export default async function OrderDetailPage({ params }: PageProps) {
  const { orderId } = params;

  // Always fetch fresh data to avoid cache issues with evidence updates
  const orderResponse = await getOrderById(orderId, true);

  if (!orderResponse?.success || !orderResponse.data) {
    notFound();
  }

  return (
    <section className="flex flex-col min-h-[90vh]">
      <OrderDetailClient order={orderResponse.data} />
    </section>
  );
}
