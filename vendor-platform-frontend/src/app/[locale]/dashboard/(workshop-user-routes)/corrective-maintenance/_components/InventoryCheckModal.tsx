'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Package, AlertTriangle, CheckCircle, Clock, ShoppingCart } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface Part {
  name: string;
  partNumber?: string;
  quantity: number;
  unitCost: number;
  totalCost: number;
  supplier: string;
}

interface PartAvailability {
  partNumber?: string;
  name: string;
  quantity: number;
  isAvailable: boolean;
  availableQuantity: number;
  estimatedArrival?: string;
  needsOrdering: boolean;
  supplier?: {
    name: string;
    leadTime: number;
    minimumOrderQuantity: number;
  };
}

interface InventoryCheckResult {
  allPartsAvailable: boolean;
  partsAvailability: PartAvailability[];
  partsNeedingOrder: number;
  summary: {
    totalParts: number;
    availableParts: number;
    unavailableParts: number;
  };
}

interface InventoryCheckModalProps {
  isOpen: boolean;
  onClose: () => void;
  parts: Part[];
  vehicleInfo?: {
    brand: string;
    model: string;
    year: number;
  };
  onProceed: (canProceed: boolean, inventoryResult: InventoryCheckResult) => void;
}

export default function InventoryCheckModal({
  isOpen,
  onClose,
  parts,
  vehicleInfo,
  onProceed,
}: InventoryCheckModalProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [inventoryResult, setInventoryResult] = useState<InventoryCheckResult | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    if (isOpen && parts.length > 0) {
      checkInventoryAvailability();
    }
  }, [isOpen, parts]);

  const checkInventoryAvailability = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/corrective-maintenance/inventory/check-availability', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          parts,
          vehicleInfo,
        }),
      });

      if (!response.ok) {
        throw new Error('Error checking inventory availability');
      }

      const data = await response.json();
      setInventoryResult(data.data);
    } catch (error) {
      console.error('Error checking inventory:', error);
      toast({
        title: 'Error',
        description: 'No se pudo verificar la disponibilidad del inventario',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (part: PartAvailability) => {
    if (part.isAvailable) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    } else if (part.availableQuantity > 0) {
      return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    } else {
      return <ShoppingCart className="h-5 w-5 text-red-500" />;
    }
  };

  const getStatusBadge = (part: PartAvailability) => {
    if (part.isAvailable) {
      return <Badge variant="default" className="bg-green-100 text-green-800">Disponible</Badge>;
    } else if (part.availableQuantity > 0) {
      return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Stock Parcial</Badge>;
    } else {
      return <Badge variant="destructive">Sin Stock</Badge>;
    }
  };

  const handleProceed = (forceStart: boolean = false) => {
    if (inventoryResult) {
      onProceed(inventoryResult.allPartsAvailable || forceStart, inventoryResult);
    }
    onClose();
  };

  const formatEstimatedArrival = (dateString?: string) => {
    if (!dateString) return 'No disponible';
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Verificación de Inventario
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Verificando disponibilidad...</span>
          </div>
        ) : inventoryResult ? (
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total de Partes</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{inventoryResult.summary.totalParts}</div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Disponibles</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {inventoryResult.summary.availableParts}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Requieren Pedido</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">
                    {inventoryResult.partsNeedingOrder}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Status Alert */}
            {inventoryResult.allPartsAvailable ? (
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  ✅ Todas las partes están disponibles. El servicio puede iniciarse inmediatamente.
                </AlertDescription>
              </Alert>
            ) : (
              <Alert className="border-yellow-200 bg-yellow-50">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <AlertDescription className="text-yellow-800">
                  ⚠️ Algunas partes no están disponibles. Se requiere gestión de inventario.
                </AlertDescription>
              </Alert>
            )}

            {/* Parts Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Detalle de Partes</span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowDetails(!showDetails)}
                  >
                    {showDetails ? 'Ocultar' : 'Ver'} Detalles
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {inventoryResult.partsAvailability.map((part, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {getStatusIcon(part)}
                        <div>
                          <div className="font-medium">{part.name}</div>
                          {part.partNumber && (
                            <div className="text-sm text-gray-500">P/N: {part.partNumber}</div>
                          )}
                          <div className="text-sm text-gray-600">
                            Requerido: {part.quantity} | Disponible: {part.availableQuantity}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        {getStatusBadge(part)}
                        {showDetails && part.supplier && (
                          <div className="text-xs text-gray-500">
                            <div>Proveedor: {part.supplier.name}</div>
                            <div>Tiempo entrega: {part.supplier.leadTime} días</div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Parts Needing Order */}
            {inventoryResult.partsNeedingOrder > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-orange-600">
                    <Clock className="h-5 w-5" />
                    Partes que Requieren Pedido
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {inventoryResult.partsAvailability
                      .filter(part => part.needsOrdering)
                      .map((part, index) => (
                        <div key={index} className="flex justify-between items-center p-2 bg-orange-50 rounded">
                          <span className="font-medium">{part.name}</span>
                          <div className="text-sm text-gray-600">
                            {part.estimatedArrival ? (
                              `Llegada estimada: ${formatEstimatedArrival(part.estimatedArrival)}`
                            ) : (
                              'Tiempo de entrega por confirmar'
                            )}
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end gap-3">
              <Button variant="outline" onClick={onClose}>
                Cancelar
              </Button>

              {inventoryResult.allPartsAvailable ? (
                <Button onClick={() => handleProceed(false)} className="bg-green-600 hover:bg-green-700">
                  Iniciar Servicio
                </Button>
              ) : (
                <>
                  <Button
                    variant="outline"
                    onClick={() => handleProceed(true)}
                    className="border-orange-300 text-orange-600 hover:bg-orange-50"
                  >
                    Iniciar Sin Inventario
                  </Button>
                  <Button onClick={() => {/* TODO: Navigate to inventory management */}}>
                    Gestionar Inventario
                  </Button>
                </>
              )}
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <p>No se pudo cargar la información del inventario.</p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
