'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Clock, AlertTriangle, CheckCircle, Users, User } from 'lucide-react';
import { Quotation, CorrectiveMaintenanceOrder } from '../types';
import { useApprovalEscalation } from '../_hooks/useApprovalEscalation';

interface ApprovalEscalationManagerProps {
  quotations: Quotation[];
  orders: CorrectiveMaintenanceOrder[];
  onQuotationUpdate: (quotationId: string, updates: Partial<Quotation>) => void;
}

export default function ApprovalEscalationManager({
  quotations,
  orders,
  onQuotationUpdate
}: ApprovalEscalationManagerProps) {
  const {
    isProcessing,
    pendingCustomerQuotations,
    overdueCustomerQuotations,
    calculateTimeRemaining,
    manualEscalateToFleet,
    manualSendReminder,
    stats
  } = useApprovalEscalation({
    quotations,
    onQuotationUpdate,
    autoEscalateEnabled: true,
    checkIntervalMinutes: 5
  });

  // Filter quotations that are escalated to fleet
  const escalatedQuotations = quotations.filter(q =>
    q.status === 'escalated-to-fleet' ||
    (q.approvalType === 'fleet' && q.escalatedToFleetAt)
  );



  const getOrderInfo = (orderId: string) => {
    return orders.find(order => order._id === orderId);
  };

  if (pendingCustomerQuotations.length === 0 && escalatedQuotations.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Gestión de Aprobaciones
            <Badge variant="outline" className="ml-auto">
              Última verificación: {stats.lastCheckTime.toLocaleTimeString()}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <p>No hay cotizaciones pendientes de gestión de aprobación</p>
            <p className="text-sm mt-2">
              Sistema de escalación automática activo
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Pending Customer Approvals */}
      {pendingCustomerQuotations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5 text-blue-600" />
              Pendientes de Aprobación del Cliente
              <Badge variant="secondary">{pendingCustomerQuotations.length}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {pendingCustomerQuotations.map(quotation => {
                const order = getOrderInfo(quotation.orderId);
                const isOverdue = quotation.customerApprovalDeadline &&
                  new Date(quotation.customerApprovalDeadline) < new Date();
                const timeRemaining = quotation.customerApprovalDeadline ?
                  calculateTimeRemaining(quotation.customerApprovalDeadline) : 'Sin límite';

                return (
                  <div key={quotation._id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{quotation.quotationNumber}</h4>
                          {isOverdue && (
                            <Badge variant="destructive">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              Vencido
                            </Badge>
                          )}
                        </div>

                        {order && (
                          <p className="text-sm text-gray-600">
                            Vehículo: {order.vehicle?.brand} {order.vehicle?.model} - {order.vehicle?.carPlates?.plates}
                          </p>
                        )}

                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {timeRemaining}
                          </span>
                          <span>Total: ${quotation.totalCost.toLocaleString()}</span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => manualSendReminder(quotation._id)}
                          disabled={isProcessing === quotation._id}
                        >
                          Recordar
                        </Button>

                        <Button
                          variant={isOverdue ? "destructive" : "outline"}
                          size="sm"
                          onClick={() => manualEscalateToFleet(quotation._id)}
                          disabled={isProcessing === quotation._id}
                        >
                          {isOverdue ? 'Escalar Ahora' : 'Escalar a Fleet'}
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Escalated to Fleet */}
      {escalatedQuotations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-orange-600" />
              Escaladas a Fleet
              <Badge variant="secondary">{escalatedQuotations.length}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {escalatedQuotations.map(quotation => {
                const order = getOrderInfo(quotation.orderId);

                return (
                  <div key={quotation._id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">{quotation.quotationNumber}</h4>
                          <Badge variant="outline">Fleet</Badge>
                        </div>

                        {order && (
                          <p className="text-sm text-gray-600">
                            Vehículo: {order.vehicle?.brand} {order.vehicle?.model} - {order.vehicle?.carPlates?.plates}
                          </p>
                        )}

                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>Total: ${quotation.totalCost.toLocaleString()}</span>
                          {quotation.escalatedToFleetAt && (
                            <span>
                              Escalada: {new Date(quotation.escalatedToFleetAt).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
