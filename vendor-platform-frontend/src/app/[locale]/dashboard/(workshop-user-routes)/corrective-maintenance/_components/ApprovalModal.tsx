'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, CheckCircle, XCircle, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Quotation, formatCurrency, formatDuration } from '../types';
import { processApproval } from '../_actions/processApproval';
import { submitQuotation } from '../_actions/submitQuotation';

const approvalSchema = z.object({
  decisions: z.array(z.object({
    serviceId: z.string(),
    isApproved: z.boolean(),
    rejectionReason: z.string().optional(),
  })).min(1, 'Debe tomar al menos una decisión'),
});

type ApprovalFormData = z.infer<typeof approvalSchema>;

interface ApprovalModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  quotation?: Quotation;
}

export default function ApprovalModal({ isOpen, onClose, onSuccess, quotation }: ApprovalModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [serviceDecisions, setServiceDecisions] = useState<Record<string, { approved: boolean; reason?: string }>>({});

  const form = useForm<ApprovalFormData>({
    resolver: zodResolver(approvalSchema),
    defaultValues: {
      decisions: [],
    },
  });

  // Initialize form when quotation changes
  useEffect(() => {
    if (quotation && isOpen) {
      const initialDecisions = quotation.services.map(service => ({
        serviceId: service.serviceId,
        isApproved: false,
        rejectionReason: '',
      }));
      form.setValue('decisions', initialDecisions);
      setServiceDecisions({});
    }
  }, [quotation, isOpen, form]);

  const onSubmit = async (data: ApprovalFormData) => {
    if (!quotation) {
      return;
    }

    setIsSubmitting(true);
    try {
      // First, check if quotation needs to be submitted for approval
      if (quotation.status !== 'pending-approval') {
        const submitResponse = await submitQuotation(quotation._id);

        if (!submitResponse.success) {
          toast({
            title: 'Error',
            description: submitResponse.message || 'Error al enviar la cotización para aprobación',
            variant: 'destructive',
          });
          return;
        }
      }

      // Now process the approval
      const response = await processApproval(quotation._id, data);

      if (response.success) {
        toast({
          title: 'Éxito',
          description: 'Aprobación procesada exitosamente',
        });
        form.reset();
        setServiceDecisions({});
        onSuccess();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Error al procesar la aprobación',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Error inesperado al procesar la aprobación',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleServiceDecision = (serviceId: string, approved: boolean, reason?: string) => {
    setServiceDecisions(prev => ({
      ...prev,
      [serviceId]: { approved, reason: reason || (approved ? '' : 'Servicio rechazado') }
    }));

    // Update form data
    const currentDecisions = form.getValues('decisions');
    let updatedDecisions = currentDecisions;

    // If the service doesn't exist in decisions, add it
    const existingIndex = currentDecisions.findIndex(d => d.serviceId === serviceId);
    if (existingIndex >= 0) {
      updatedDecisions = currentDecisions.map(decision =>
        decision.serviceId === serviceId
          ? { ...decision, isApproved: approved, rejectionReason: reason || (approved ? '' : 'Servicio rechazado') }
          : decision
      );
    } else {
      updatedDecisions = [
        ...currentDecisions,
        {
          serviceId,
          isApproved: approved,
          rejectionReason: reason || (approved ? '' : 'Servicio rechazado')
        }
      ];
    }

    form.setValue('decisions', updatedDecisions);
  };

  const handleApproveAll = () => {
    if (!quotation) return;

    quotation.services.forEach(service => {
      handleServiceDecision(service.serviceId, true);
    });
  };

  const handleRejectAll = () => {
    if (!quotation) return;

    quotation.services.forEach(service => {
      handleServiceDecision(service.serviceId, false, 'Rechazado en lote');
    });
  };

  const handleClose = () => {
    form.reset();
    setServiceDecisions({});
    onClose();
  };

  if (!quotation) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>No hay cotización para aprobar</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-gray-600">
              No se encontró una cotización válida para procesar.
            </p>
          </div>
          <DialogFooter>
            <Button onClick={handleClose}>Cerrar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  const approvedCount = Object.values(serviceDecisions).filter(d => d.approved).length;
  const rejectedCount = Object.values(serviceDecisions).filter(d => !d.approved && serviceDecisions[Object.keys(serviceDecisions).find(k => serviceDecisions[k] === d) || '']).length;
  const totalCostApproved = quotation.services
    .filter(service => serviceDecisions[service.serviceId]?.approved)
    .reduce((sum, service) => sum + service.estimatedCost, 0);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Procesar Aprobación de Cotización</DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Status Information */}
          {quotation.status !== 'pending-approval' && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5" />
                </div>
                <div>
                  <h4 className="text-sm font-medium text-blue-800">
                    Cotización será enviada para aprobación
                  </h4>
                  <p className="text-sm text-blue-700 mt-1">
                    Esta cotización está en estado "{quotation.status}". Primero se enviará para aprobación
                    y luego se procesarán las decisiones que tomes aquí.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Quotation Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Cotización #{quotation.quotationNumber}</span>
                <div className="flex items-center gap-2">
                  <Badge variant={quotation.status === 'pending-approval' ? 'default' : 'secondary'}>
                    {quotation.status === 'pending-approval' ? 'Pendiente de Aprobación' :
                     quotation.status === 'draft' ? 'Borrador' :
                     quotation.status === 'approved' ? 'Aprobada' :
                     quotation.status === 'rejected' ? 'Rechazada' :
                     quotation.status === 'partially-approved' ? 'Parcialmente Aprobada' : quotation.status}
                  </Badge>
                  {quotation.status !== 'pending-approval' && (
                    <Badge variant="outline" className="text-orange-600">
                      Se enviará para aprobación
                    </Badge>
                  )}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Total de Servicios:</p>
                  <p className="font-medium">{quotation.services.length}</p>
                </div>
                <div>
                  <p className="text-gray-500">Costo Total:</p>
                  <p className="font-medium">{formatCurrency(quotation.totalCost)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Válida hasta:</p>
                  <p className="font-medium">{new Date(quotation.validUntil).toLocaleDateString('es-ES')}</p>
                </div>
                <div>
                  <p className="text-gray-500">Estado:</p>
                  <p className="font-medium">{quotation.status}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Bulk Actions */}
          <div className="flex gap-2 flex-wrap">
            <Button type="button" variant="outline" onClick={handleApproveAll}>
              <CheckCircle className="h-4 w-4 mr-2" />
              Aprobar Todo
            </Button>
            <Button type="button" variant="outline" onClick={handleRejectAll}>
              <XCircle className="h-4 w-4 mr-2" />
              Rechazar Todo
            </Button>

          </div>

          {/* Services List */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Servicios para Aprobación</h3>

            {quotation.services.map((service, index) => {
              const decision = serviceDecisions[service.serviceId];
              const isApproved = decision?.approved || false;
              const isRejected = decision && !decision.approved;

              return (
                <Card key={service.serviceId} className={`border-2 ${
                  isApproved ? 'border-green-200 bg-green-50' :
                  isRejected ? 'border-red-200 bg-red-50' :
                  'border-gray-200'
                }`}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">{service.serviceName}</CardTitle>
                      <div className="flex items-center gap-2">
                        {isApproved && <CheckCircle className="h-5 w-5 text-green-600" />}
                        {isRejected && <XCircle className="h-5 w-5 text-red-600" />}
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">{service.description}</p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Costo Total:</p>
                        <p className="font-medium">{formatCurrency(service.estimatedCost)}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Duración:</p>
                        <p className="font-medium">{formatDuration(service.estimatedDuration)}</p>
                      </div>
                    </div>

                    <Separator />

                    {/* Approval Decision */}
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Decisión de Aprobación</Label>
                      <div className="flex gap-4">
                        <Button
                          type="button"
                          variant={isApproved ? "default" : "outline"}
                          size="sm"
                          onClick={() => handleServiceDecision(service.serviceId, true)}
                          className={isApproved ? "bg-green-600 hover:bg-green-700" : ""}
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Aprobar
                        </Button>
                        <Button
                          type="button"
                          variant={isRejected ? "destructive" : "outline"}
                          size="sm"
                          onClick={() => handleServiceDecision(service.serviceId, false, decision?.reason || 'Servicio rechazado')}
                          className={isRejected ? "bg-red-600 hover:bg-red-700" : ""}
                        >
                          <XCircle className="h-4 w-4 mr-2" />
                          Rechazar
                        </Button>
                      </div>

                      {/* Rejection Reason */}
                      {isRejected && (
                        <div className="space-y-2">
                          <Label htmlFor={`reason-${service.serviceId}`}>
                            Motivo del Rechazo
                          </Label>
                          <Textarea
                            id={`reason-${service.serviceId}`}
                            placeholder="Explique el motivo del rechazo..."
                            value={decision?.reason || ''}
                            onChange={(e) =>
                              handleServiceDecision(service.serviceId, false, e.target.value)
                            }
                            rows={2}
                          />
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Summary */}
          {Object.keys(serviceDecisions).length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Resumen de Decisiones
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Servicios Aprobados:</p>
                    <p className="font-medium text-green-600">{approvedCount}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Servicios Rechazados:</p>
                    <p className="font-medium text-red-600">{rejectedCount}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Costo Aprobado:</p>
                    <p className="font-medium">{formatCurrency(totalCostApproved)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <DialogFooter className="flex-col sm:flex-row gap-3">
            {/* Decision Status */}
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>Decisiones tomadas: {Object.keys(serviceDecisions).length} de {quotation?.services.length || 0}</span>
              {Object.keys(serviceDecisions).length === 0 && (
                <Badge variant="outline" className="text-orange-600">
                  Selecciona al menos un servicio
                </Badge>
              )}
            </div>

            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || Object.keys(serviceDecisions).length === 0}
                className="min-w-[150px]"
              >
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSubmitting ? 'Procesando...' : 'Procesar Aprobación'}
              </Button>
            </div>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
