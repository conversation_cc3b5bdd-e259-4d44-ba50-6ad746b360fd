'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import {
  Loader2, Play, Pause, CheckCircle, Clock, Upload, X, Image,
  ChevronRight, AlertTriangle, Timer, Camera, MessageSquare
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CorrectiveService, formatCurrency, formatDuration } from '../types';
import { updateServiceExecution } from '../_actions/updateServiceExecution';

const serviceExecutionSchema = z.object({
  action: z.enum(['start', 'pause', 'resume', 'complete']),
  notes: z.string().optional(),
  evidence: z.array(z.instanceof(File)).optional(),
  completionNotes: z.string().optional(),
  milestoneId: z.string().optional(),
  phase: z.string().optional(),
  phaseNotes: z.string().optional(),
});

type ServiceExecutionFormData = z.infer<typeof serviceExecutionSchema>;

interface ServicePhase {
  id: string;
  name: string;
  description: string;
  estimatedDuration: number; // in minutes
  status: 'pending' | 'in-progress' | 'completed' | 'skipped';
  actualDuration?: number;
  startedAt?: string;
  completedAt?: string;
  evidence: string[];
  notes?: string;
  required: boolean;
}

interface ServiceMilestone {
  id: string;
  name: string;
  description: string;
  phase: string;
  status: 'pending' | 'completed';
  completedAt?: string;
  evidence: string[];
  notes?: string;
}

interface EnhancedServiceExecutionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  service: CorrectiveService;
  orderId: string;
}

export default function EnhancedServiceExecutionModal({
  isOpen,
  onClose,
  onSuccess,
  service,
  orderId
}: EnhancedServiceExecutionModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [serviceStartTime, setServiceStartTime] = useState<Date | null>(null);
  const [elapsedTime, setElapsedTime] = useState(0);
  const [currentPhase, setCurrentPhase] = useState<string>('preparation');
  const [activeTab, setActiveTab] = useState('execution');

  // Enhanced service phases with more detail
  const [servicePhases, setServicePhases] = useState<ServicePhase[]>([
    {
      id: 'preparation',
      name: 'Preparación',
      description: 'Preparación del área de trabajo, herramientas y verificación inicial',
      estimatedDuration: 15,
      status: 'pending' as const,
      evidence: [],
      required: true
    },
    {
      id: 'diagnosis',
      name: 'Diagnóstico',
      description: 'Diagnóstico detallado y confirmación del problema',
      estimatedDuration: 30,
      status: 'pending' as const,
      evidence: [],
      required: true
    },
    {
      id: 'parts_verification',
      name: 'Verificación de Refacciones',
      description: 'Verificación y preparación de refacciones necesarias',
      estimatedDuration: 10,
      status: 'pending' as const,
      evidence: [],
      required: service.parts.length > 0
    },
    {
      id: 'execution',
      name: 'Ejecución Principal',
      description: 'Ejecución del trabajo principal del servicio',
      estimatedDuration: Math.max(service.estimatedDuration - 75, 30),
      status: 'pending' as const,
      evidence: [],
      required: true
    },
    {
      id: 'testing',
      name: 'Pruebas y Verificación',
      description: 'Pruebas de funcionamiento y verificación de calidad',
      estimatedDuration: 20,
      status: 'pending' as const,
      evidence: [],
      required: true
    },
    {
      id: 'cleanup',
      name: 'Limpieza y Finalización',
      description: 'Limpieza del área de trabajo y documentación final',
      estimatedDuration: 10,
      status: 'pending' as const,
      evidence: [],
      required: true
    }
  ].filter(phase => phase.required));

  // Service milestones
  const [serviceMilestones, setServiceMilestones] = useState<ServiceMilestone[]>([
    {
      id: 'initial_inspection',
      name: 'Inspección Inicial',
      description: 'Inspección inicial del vehículo completada',
      phase: 'preparation',
      status: 'pending' as const,
      evidence: []
    },
    {
      id: 'problem_confirmed',
      name: 'Problema Confirmado',
      description: 'Problema diagnosticado y confirmado',
      phase: 'diagnosis',
      status: 'pending' as const,
      evidence: []
    },
    {
      id: 'parts_ready',
      name: 'Refacciones Listas',
      description: 'Todas las refacciones están preparadas',
      phase: 'parts_verification',
      status: 'pending' as const,
      evidence: []
    },
    {
      id: 'work_completed',
      name: 'Trabajo Completado',
      description: 'Trabajo principal del servicio completado',
      phase: 'execution',
      status: 'pending' as const,
      evidence: []
    },
    {
      id: 'quality_verified',
      name: 'Calidad Verificada',
      description: 'Calidad del trabajo verificada y aprobada',
      phase: 'testing',
      status: 'pending' as const,
      evidence: []
    }
  ]);

  const form = useForm<ServiceExecutionFormData>({
    resolver: zodResolver(serviceExecutionSchema),
    defaultValues: {
      action: 'start',
      notes: '',
      evidence: [],
      completionNotes: '',
      phase: 'preparation',
    },
  });

  // Timer for elapsed time when service is in progress
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (service.status === 'in-progress' && serviceStartTime) {
      interval = setInterval(() => {
        const now = new Date();
        const elapsed = Math.floor((now.getTime() - serviceStartTime.getTime()) / 1000 / 60);
        setElapsedTime(elapsed);
      }, 60000);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [service.status, serviceStartTime]);

  const getCurrentPhaseData = () => {
    return servicePhases.find(phase => phase.id === currentPhase) || servicePhases[0];
  };

  const getNextPhase = () => {
    const currentIndex = servicePhases.findIndex(phase => phase.id === currentPhase);
    return currentIndex < servicePhases.length - 1 ? servicePhases[currentIndex + 1] : null;
  };

  const getTotalEstimatedTime = () => {
    return servicePhases.reduce((total, phase) => total + phase.estimatedDuration, 0);
  };

  const getCompletedTime = () => {
    return servicePhases
      .filter(phase => phase.status === 'completed')
      .reduce((total, phase) => total + (phase.actualDuration || phase.estimatedDuration), 0);
  };

  const getOverallProgress = () => {
    const completedPhases = servicePhases.filter(phase => phase.status === 'completed').length;
    const inProgressPhases = servicePhases.filter(phase => phase.status === 'in-progress').length * 0.5;
    return Math.round(((completedPhases + inProgressPhases) / servicePhases.length) * 100);
  };

  const onSubmit = async (data: ServiceExecutionFormData) => {
    setIsSubmitting(true);
    try {
      const executionData = {
        action: data.action,
        notes: data.notes,
        completionNotes: data.completionNotes,
        evidence: selectedFiles,
        timestamp: new Date().toISOString(),
      };

      const response = await updateServiceExecution(service._id, orderId, executionData);

      if (response.success) {
        toast({
          title: 'Éxito',
          description: getSuccessMessage(data.action),
        });

        // Update local state based on action
        if (data.action === 'start') {
          setServiceStartTime(new Date());
          updatePhaseStatus('preparation', 'in-progress');
        } else if (data.action === 'complete') {
          updatePhaseStatus(currentPhase, 'completed');
        }

        form.reset();
        setSelectedFiles([]);
        onSuccess();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Error al actualizar el servicio',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error updating service execution:', error);
      toast({
        title: 'Error',
        description: 'Error inesperado al actualizar el servicio',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const updatePhaseStatus = (phaseId: string, status: ServicePhase['status']) => {
    setServicePhases(prev => prev.map(phase =>
      phase.id === phaseId
        ? {
            ...phase,
            status,
            startedAt: status === 'in-progress' ? new Date().toISOString() : phase.startedAt,
            completedAt: status === 'completed' ? new Date().toISOString() : phase.completedAt,
          }
        : phase
    ));
  };

  const moveToNextPhase = () => {
    const nextPhase = getNextPhase();
    if (nextPhase) {
      setCurrentPhase(nextPhase.id);
    }
  };

  const completeMilestone = (milestoneId: string) => {
    setServiceMilestones(prev => prev.map(milestone =>
      milestone.id === milestoneId
        ? { ...milestone, status: 'completed' as const, completedAt: new Date().toISOString() }
        : milestone
    ));
  };

  const getSuccessMessage = (action: string) => {
    switch (action) {
      case 'start': return 'Servicio iniciado exitosamente';
      case 'pause': return 'Servicio pausado exitosamente';
      case 'resume': return 'Servicio reanudado exitosamente';
      case 'complete': return 'Servicio completado exitosamente';
      default: return 'Servicio actualizado exitosamente';
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedFiles(prev => [...prev, ...files]);
  };

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleClose = () => {
    form.reset();
    setSelectedFiles([]);
    onClose();
  };

  const getPhaseStatusColor = (status: ServicePhase['status']) => {
    switch (status) {
      case 'pending': return 'bg-gray-100 text-gray-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'skipped': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPhaseStatusIcon = (status: ServicePhase['status']) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'in-progress': return <Timer className="h-4 w-4 animate-pulse" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'skipped': return <ChevronRight className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const currentPhaseData = getCurrentPhaseData();
  const canStartService = service.status === 'not-started' || service.status === 'waiting-for-parts';
  const canCompletePhase = currentPhaseData.status === 'in-progress';
  const canStartPhase = currentPhaseData.status === 'pending';

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Ejecución Avanzada del Servicio</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="execution">Ejecución</TabsTrigger>
            <TabsTrigger value="phases">Fases</TabsTrigger>
            <TabsTrigger value="evidence">Evidencia</TabsTrigger>
          </TabsList>

          <TabsContent value="execution" className="space-y-6">
            {/* Service Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>{service.serviceName}</span>
                  <Badge variant="secondary" className={getStatusColor(service.status)}>
                    {getStatusLabel(service.status)}
                  </Badge>
                </CardTitle>
                <p className="text-sm text-gray-600">{service.description}</p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                  <div>
                    <p className="text-gray-500">Costo Estimado:</p>
                    <p className="font-medium">{formatCurrency(service.estimatedCost)}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Duración Estimada:</p>
                    <p className="font-medium">{formatDuration(getTotalEstimatedTime())}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Tiempo Transcurrido:</p>
                    <p className="font-medium">{elapsedTime} min</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Tiempo Completado:</p>
                    <p className="font-medium">{getCompletedTime()} min</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Refacciones:</p>
                    <p className="font-medium">{service.parts.length}</p>
                  </div>
                </div>

                {/* Overall Progress */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm font-medium">Progreso General</Label>
                    <span className="text-sm text-gray-500">{getOverallProgress()}%</span>
                  </div>
                  <Progress value={getOverallProgress()} className="h-3" />
                </div>
              </CardContent>
            </Card>

            {/* Current Phase Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getPhaseStatusIcon(currentPhaseData.status)}
                  Fase Actual: {currentPhaseData.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">{currentPhaseData.description}</p>
                <div className="flex items-center gap-4 mb-4">
                  <Badge className={getPhaseStatusColor(currentPhaseData.status)}>
                    {currentPhaseData.status}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    Duración estimada: {currentPhaseData.estimatedDuration} min
                  </span>
                </div>

                {/* Phase Actions */}
                <div className="flex gap-2">
                  {canStartService && (
                    <Button
                      onClick={() => {
                        form.setValue('action', 'start');
                        form.handleSubmit(onSubmit)();
                      }}
                      className="bg-green-600 hover:bg-green-700"
                      disabled={isSubmitting}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Iniciar Servicio
                    </Button>
                  )}

                  {canStartPhase && service.status === 'in-progress' && (
                    <Button
                      onClick={() => {
                        form.setValue('action', 'start');
                        form.setValue('phase', currentPhaseData.id);
                        form.handleSubmit(onSubmit)();
                      }}
                      disabled={isSubmitting}
                    >
                      <Play className="h-4 w-4 mr-2" />
                      Iniciar Fase
                    </Button>
                  )}

                  {canCompletePhase && (
                    <Button
                      onClick={() => {
                        form.setValue('action', 'complete');
                        form.setValue('phase', currentPhaseData.id);
                        form.handleSubmit(onSubmit)();
                      }}
                      disabled={isSubmitting}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Completar Fase
                    </Button>
                  )}

                  {service.status === 'in-progress' && getOverallProgress() === 100 && (
                    <Button
                      onClick={() => {
                        form.setValue('action', 'complete');
                        form.handleSubmit(onSubmit)();
                      }}
                      className="bg-blue-600 hover:bg-blue-700"
                      disabled={isSubmitting}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Finalizar Servicio
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Quick Notes */}
            <Card>
              <CardHeader>
                <CardTitle>Notas Rápidas</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  <Textarea
                    {...form.register('notes')}
                    placeholder="Agregar nota sobre el progreso actual..."
                    rows={3}
                  />
                  <Button type="submit" size="sm" disabled={isSubmitting}>
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Agregar Nota
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="phases" className="space-y-4">
            <div className="grid gap-4">
              {servicePhases.map((phase, index) => (
                <Card key={phase.id} className={phase.id === currentPhase ? 'ring-2 ring-blue-500' : ''}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-100 text-sm font-medium">
                          {index + 1}
                        </div>
                        <div>
                          <CardTitle className="text-lg">{phase.name}</CardTitle>
                          <p className="text-sm text-gray-600">{phase.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getPhaseStatusColor(phase.status)}>
                          {getPhaseStatusIcon(phase.status)}
                          <span className="ml-1 capitalize">{phase.status}</span>
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500">Duración Estimada:</p>
                        <p className="font-medium">{phase.estimatedDuration} min</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Duración Real:</p>
                        <p className="font-medium">
                          {phase.actualDuration ? `${phase.actualDuration} min` : 'N/A'}
                        </p>
                      </div>
                      <div>
                        <p className="text-gray-500">Evidencias:</p>
                        <p className="font-medium">{phase.evidence.length}</p>
                      </div>
                    </div>

                    {phase.notes && (
                      <Alert className="mt-3">
                        <AlertDescription>{phase.notes}</AlertDescription>
                      </Alert>
                    )}

                    {/* Phase Milestones */}
                    <div className="mt-3">
                      <p className="text-sm font-medium mb-2">Hitos de la Fase:</p>
                      {serviceMilestones
                        .filter(milestone => milestone.phase === phase.id)
                        .map(milestone => (
                          <div key={milestone.id} className="flex items-center justify-between p-2 bg-gray-50 rounded mb-1">
                            <span className="text-sm">{milestone.name}</span>
                            <Badge variant={milestone.status === 'completed' ? 'default' : 'secondary'}>
                              {milestone.status === 'completed' ? 'Completado' : 'Pendiente'}
                            </Badge>
                          </div>
                        ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="evidence" className="space-y-4">
            {/* Evidence Upload */}
            <Card>
              <CardHeader>
                <CardTitle>Subir Evidencia</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
                  <div className="text-center">
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="mt-4">
                      <label htmlFor="evidence-upload" className="cursor-pointer">
                        <span className="mt-2 block text-sm font-medium text-gray-900">
                          Subir archivos de evidencia
                        </span>
                        <span className="mt-1 block text-sm text-gray-500">
                          PNG, JPG, MP4 hasta 10MB cada uno
                        </span>
                      </label>
                      <input
                        id="evidence-upload"
                        type="file"
                        multiple
                        accept="image/*,video/*"
                        onChange={handleFileChange}
                        className="hidden"
                      />
                    </div>
                  </div>
                </div>

                {/* Selected Files */}
                {selectedFiles.length > 0 && (
                  <div className="space-y-2">
                    <Label>Archivos Seleccionados</Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {selectedFiles.map((file, index) => (
                        <div key={index} className="relative border rounded-lg p-2 bg-gray-50">
                          <div className="flex items-center space-x-2">
                            <Image className="h-4 w-4 text-gray-500" />
                            <span className="text-xs truncate flex-1">{file.name}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFile(index)}
                              className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                          </div>
                        </div>
                      ))}
                    </div>

                    <Button
                      onClick={() => {
                        form.setValue('action', 'resume');
                        form.setValue('evidence', selectedFiles);
                        form.handleSubmit(onSubmit)();
                      }}
                      disabled={isSubmitting}
                      className="w-full"
                    >
                      <Camera className="h-4 w-4 mr-2" />
                      Subir Evidencia de Fase Actual
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Evidence by Phase */}
            <div className="space-y-4">
              {servicePhases.map(phase => (
                <Card key={phase.id}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>Evidencia - {phase.name}</span>
                      <Badge className={getPhaseStatusColor(phase.status)}>
                        {phase.evidence.length} archivos
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {phase.evidence.length > 0 ? (
                      <div className="grid grid-cols-4 gap-2">
                        {phase.evidence.map((evidenceUrl, index) => (
                          <div key={index} className="aspect-square bg-gray-100 rounded border">
                            <img
                              src={evidenceUrl}
                              alt={`Evidencia ${index + 1}`}
                              className="w-full h-full object-cover rounded"
                            />
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500 text-center py-4">
                        No hay evidencia para esta fase
                      </p>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-6">
          <Button type="button" variant="outline" onClick={handleClose}>
            Cerrar
          </Button>
          {isSubmitting && (
            <Button disabled>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Procesando...
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

// Helper functions
function getStatusColor(status: string) {
  switch (status) {
    case 'not-started': return 'bg-gray-100 text-gray-800';
    case 'in-progress': return 'bg-blue-100 text-blue-800';
    case 'completed': return 'bg-green-100 text-green-800';
    case 'waiting-for-parts': return 'bg-yellow-100 text-yellow-800';
    case 'cancelled': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
}

function getStatusLabel(status: string) {
  switch (status) {
    case 'not-started': return 'No Iniciado';
    case 'in-progress': return 'En Progreso';
    case 'completed': return 'Completado';
    case 'waiting-for-parts': return 'Esperando Refacciones';
    case 'cancelled': return 'Cancelado';
    default: return 'Desconocido';
  }
}