/* eslint-disable @typescript-eslint/no-use-before-define */
import moment from 'moment';
// import { ValidRegion, gigstackRequests } from '../../../services/tokenAssignGigstack';
import { AsyncController } from '../../../types&interfaces/types';
import TempSuscriptionPayments from '../model/tempSuscriptionPayment.model';
// import { getCurrentDateTime } from '../../../services/timestamps';
import axios from 'axios';
import Associate from '../../../models/associateSchema';
import { PAYMENTS_API_KEY, PAYMENTS_API_URL } from '../../../constants/payments-api';
import { getCurrentDateTime } from '../../../services/timestamps';
import { logger } from '../../../clean/lib/logger';

type GenerateDateNotUTC = {
  date?: string | null;
  currentTime?: boolean;
};

// const testingDate = '2024-11-21';
/**
 * Function to generate a date without changing the time zone, by default it will generate a date with 06:00:00 time
 * @param {GenerateDateNotUTC}{ date = null, removeTime = true } by default
 * @returns {Date}
 */

export function generateDateNotUTC({ date = null, currentTime = false }: GenerateDateNotUTC = {}) {
  const today = date ? moment(date) : moment();
  const year = today.year();
  const month = today.month();
  const day = today.date();

  const hour = currentTime ? today.hour() : 6;
  const minute = currentTime ? today.minute() : 0;
  const second = currentTime ? today.second() : 0;
  return new Date(Date.UTC(year, month, day, hour, minute, second));
}

/**
 * Obtiene el lunes de referencia según el día de la semana actual:
 * - Si es lunes: devuelve el mismo día
 * - Si es martes o miércoles: devuelve el lunes anterior
 * - Si es jueves a domingo: devuelve el lunes siguiente
 */
function getReferenceMonday(date: Date = new Date()): Date {
  const momentDate = moment(date);
  const dayOfWeek = momentDate.day(); // 0 = domingo, 1 = lunes, 4 = jueves

  // Si es lunes, devuelve el mismo día
  if (dayOfWeek === 1) {
    return date;
  }

  // Si es martes (2) o miércoles (3), devuelve el lunes anterior
  if (dayOfWeek === 2 || dayOfWeek === 3) {
    const mondayDate = momentDate.clone().subtract(dayOfWeek - 1, 'days');
    return generateDateNotUTC({ date: mondayDate.format('YYYY-MM-DD') });
  }

  // Si es jueves (4) a domingo (0), devuelve el lunes siguiente
  let daysToAdd = dayOfWeek === 0 ? 1 : 8 - dayOfWeek;
  const mondayDate = momentDate.clone().add(daysToAdd, 'days');
  return generateDateNotUTC({ date: mondayDate.format('YYYY-MM-DD') });
}

export const cronJobTempPaymentsHandler: AsyncController = async (_req, res) => {
  try {
    // Process active temporal subscriptions
    await processActiveTemporalSubscriptions();

    // Activate pending temporal subscriptions
    await activatePendingTemporalSubscriptions();

    return res.status(200).json({ message: 'Proceso terminado' });
  } catch (error) {
    logger.error('Error en cronJobTempPaymentsHandler', error);
    return res.status(500).json({ message: 'Error al procesar la solicitud' });
  }
};

/**
 * Procesa suscripciones temporales activas y elimina items expirados
 */
async function processActiveTemporalSubscriptions() {
  const tempPayments = await TempSuscriptionPayments.find({ status: 'active' });
  const today = generateDateNotUTC();
  const nextMondayOfToday = getReferenceMonday(today);

  logger.info(`[TEMP CRONJOB]: temporal payments status active: ${tempPayments.length}`);

  // for (const tempPayment of tempPayments) {
  //   await processTemporalPayment(tempPayment, nextMondayOfToday);
  // }
  await Promise.allSettled(
    tempPayments.map(async (tempPayment) => {
      await processTemporalPayment(tempPayment, nextMondayOfToday);
    })
  );
}

/**
 * Procesa un pago temporal individual, eliminando items expirados
 */
async function processTemporalPayment(tempPayment: any, today: Date) {
  const originalItemsCount = tempPayment.tempItems.length;
  const copyTempItems = [...tempPayment.tempItems];

  // Eliminar items expirados
  for (const tempItem of copyTempItems) {
    logger.info(`ITEM EXPIRATION CHECK - Expired?: ${tempItem.expirationDate.getTime() < today.getTime()}`);
    logger.info(`Expiration Date: ${tempItem.expirationDate}, Today: ${today}`);

    if (tempItem.expirationDate.getTime() < today.getTime()) {
      logger.info(`[TEMP CRONJOB]: removing expired item - ${tempItem.name}`);
      tempPayment.tempItems.shift();
    }
  }

  logger.info(`Stock Id: ${tempPayment.stockId}`);

  const associate = await Associate.findById(tempPayment.associateId);
  if (!associate) return;

  // Si no quedan items temporales, desactivar la suscripción
  if (tempPayment.tempItems.length === 0) {
    await handleEmptyTemporalItems(associate, tempPayment);
  } else if (tempPayment.tempItems.length < originalItemsCount) {
    // Si se eliminaron algunos items pero aún quedan, actualizar la suscripción
    await updateRemainingTemporalItems(associate, tempPayment);
  }

  logger.info('-------------------------------------------------');
}

/**
 * Maneja el caso cuando no quedan items temporales
 */
async function handleEmptyTemporalItems(associate: any, tempPayment: any) {
  logger.info('[TEMP CRONJOB]: removeTempItems TO SUBSCRIPTION - No items remaining');

  if (associate.clientId) {
    await axios.patch(
      `${PAYMENTS_API_URL}/subscriptions/${associate.clientId}/remove-temporal-products`,
      {},
      { headers: { Authorization: `Bearer ${PAYMENTS_API_KEY}` } }
    );

    await TempSuscriptionPayments.findByIdAndUpdate(tempPayment._id, {
      status: 'inactive',
      updatedAt: getCurrentDateTime(),
    });

    logger.info(`[TEMP CRONJOB]: inactive ${associate.clientId}`);
  }
}

/**
 * Actualiza los items temporales restantes después de eliminar los expirados
 */
async function updateRemainingTemporalItems(associate: any, tempPayment: any) {
  logger.info('UPDATING TEMPORAL ITEMS - Removing expired, keeping active ones');

  if (associate.clientId) {
    // Primero eliminar todos los productos temporales
    await axios.patch(
      `${PAYMENTS_API_URL}/subscriptions/${associate.clientId}/remove-temporal-products`,
      {},
      { headers: { Authorization: `Bearer ${PAYMENTS_API_KEY}` } }
    );

    // Formatear los items temporales restantes
    const formatTempItems = tempPayment.tempItems.map((item: any) => ({
      name: item.name,
      description: item.description,
      quantity: item.quantity,
      price: item.total,
      isTemporal: true,
    }));

    // Agregar los items temporales restantes
    await axios.patch(
      `${PAYMENTS_API_URL}/subscriptions/${associate.clientId}/add-temporal-products`,
      { temporalProducts: formatTempItems },
      { headers: { Authorization: `Bearer ${PAYMENTS_API_KEY}` } }
    );
  }
}

/**
 * Activa suscripciones temporales pendientes cuando llega su fecha de activación
 */
async function activatePendingTemporalSubscriptions() {
  const tempPaymentsPending = await TempSuscriptionPayments.find({ status: 'pending' });
  const today = generateDateNotUTC();
  const nextMondayOfToday = getReferenceMonday(today);

  logger.info(`[TEMP CRONJOB]: temporal payments status pending: ${tempPaymentsPending.length}`);
  logger.info(`[TEMP CRONJOB]: today ${today}, nextMondayOfToday ${nextMondayOfToday}`);

  await Promise.allSettled(
    tempPaymentsPending.map(async (tempPayment) => {
      await processPendingTemporalPayment(tempPayment, nextMondayOfToday);
    })
  );
}

/**
 * Process a pending temporal payment individually
 */
async function processPendingTemporalPayment(tempPayment: any, today: Date) {
  const associate = await Associate.findById(tempPayment.associateId);
  if (!associate) return;

  // Manejar adendum de tipo add-weeks
  if (tempPayment.adendumType === 'add-weeks') {
    await handleAddWeeksAdendum(tempPayment, today, associate);
    return;
  }

  // Manejar parada de suscripción
  const stopDate = tempPayment.stopDate;
  if (stopDate && stopDate.getTime() === today.getTime()) {
    await stopSubscription(associate, tempPayment);
    return;
  }

  // Manejar activación de suscripción
  const activationDate = tempPayment.activationDate;
  const dateToAddTempProducts = tempPayment.dateToAddTempProducts;

  // Verificar si hay un solo item temporal que expira hoy
  if (tempPayment.tempItems.length === 1) {
    const expirationDate = tempPayment.tempItems[0].expirationDate;
    if (expirationDate === today) {
      await TempSuscriptionPayments.findByIdAndUpdate(tempPayment._id, {
        status: 'inactive',
        updatedAt: getCurrentDateTime(),
      });
      return;
    }
  }

  // Formatear items temporales para la API
  const formatTempItems = tempPayment.tempItems.map((item: any) => ({
    name: item.name,
    description: item.description,
    quantity: item.quantity,
    price: item.total,
    isTemporal: true,
  }));

  // Activar suscripción si es el día de activación
  if (activationDate && activationDate.getTime() === today.getTime()) {
    // await activateSubscription(associate, tempPayment, dateToAddTempProducts, formatTempItems);
    await activateSubscription({
      associate,
      tempPayment,
      dateToAddTempProducts,
      formatTempItems,
    });
  }

  // Agregar productos temporales si es el día indicado
  if (dateToAddTempProducts && dateToAddTempProducts.getTime() === today.getTime() && associate.clientId) {
    await addTemporalProducts(associate, tempPayment, formatTempItems);
  }

  logger.info('-------------------------------------------------');
}

/**
 * Maneja adendum de tipo add-weeks
 */
async function handleAddWeeksAdendum(tempPayment: any, today: Date, associate: any) {
  const stopDate = tempPayment.stopDate;
  const activateDate = tempPayment.activationDate;

  // Detener suscripción si es la fecha de parada
  if (stopDate && stopDate.getTime() === today.getTime()) {
    logger.info(
      `[TEMP CRONJOB]: stoping subscription for add-weeks, Client ID: ${associate.clientId}, StockVehicle ID: ${tempPayment.stockId}`
    );

    await axios.patch(
      `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
      { status: false },
      { headers: { Authorization: `Bearer ${PAYMENTS_API_KEY}` } }
    );
  }

  // Activar suscripción si es la fecha de activación
  if (activateDate && activateDate.getTime() === today.getTime()) {
    logger.info(
      `[TEMP CRONJOB]: activating subscription for add-weeks, Client ID: ${associate.clientId}, StockVehicle ID: ${tempPayment.stockId}`
    );

    await axios.patch(
      `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
      { status: true },
      { headers: { Authorization: `Bearer ${PAYMENTS_API_KEY}` } }
    );

    await TempSuscriptionPayments.findByIdAndUpdate(tempPayment._id, {
      status: 'inactive',
      updatedAt: getCurrentDateTime(),
    });
  }
}

/**
 * Detiene una suscripción
 */
async function stopSubscription(associate: any, tempPayment: any) {
  logger.info(`Stopping subscription, Stock Id: ${tempPayment.stockId}`);

  await axios.patch(
    `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
    { status: false },
    { headers: { Authorization: `Bearer ${PAYMENTS_API_KEY}` } }
  );
}

/**
 * Activa una suscripción y opcionalmente agrega productos temporales
 */
async function activateSubscription(
  // associate: any,
  // tempPayment: any,
  // dateToAddTempProducts: any,
  // formatTempItems: any
  {
    associate,
    tempPayment,
    dateToAddTempProducts,
    formatTempItems,
  }: {
    associate: any;
    tempPayment: any;
    dateToAddTempProducts: any;
    formatTempItems: any;
  }
) {
  logger.info(`Activating subscription, Stock Id: ${tempPayment.stockId}`);

  await axios.patch(
    `${PAYMENTS_API_URL}/subscriptions/status/${associate.clientId}`,
    { status: true },
    { headers: { Authorization: `Bearer ${PAYMENTS_API_KEY}` } }
  );

  if (!dateToAddTempProducts && associate.clientId) {
    await addTemporalProducts(associate, tempPayment, formatTempItems);
  }
}

/**
 * Agrega productos temporales a una suscripción
 */
async function addTemporalProducts(associate: any, tempPayment: any, formatTempItems: any) {
  try {
    await axios.patch(
      `${PAYMENTS_API_URL}/subscriptions/${associate.clientId}/add-temporal-products`,
      { temporalProducts: formatTempItems },
      { headers: { Authorization: `Bearer ${PAYMENTS_API_KEY}` } }
    );
  } catch (error: any) {
    logger.error('Error al agregar temporal products', error.response?.data || error);
  }

  await TempSuscriptionPayments.findByIdAndUpdate(tempPayment._id, {
    status: 'active',
    updatedAt: getCurrentDateTime(),
  });
}
