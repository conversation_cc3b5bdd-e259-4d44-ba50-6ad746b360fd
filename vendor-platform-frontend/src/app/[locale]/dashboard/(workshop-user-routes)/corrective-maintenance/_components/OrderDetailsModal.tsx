'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Car,
  MapPin,
  Clock,
  DollarSign,
  FileText,
  User,
  Phone,
  Mail,
  Calendar,
  Wrench,
  Package,
  AlertTriangle,
  CheckCircle,
  Info,
  Activity,
  Timer,
  Play,
  Pause
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { CorrectiveMaintenanceOrder } from '../types';
import ImageModal from './ImageModal';
import {
  formatCurrency,
  formatDuration,
  getStatusColor,
  getStatusLabel,
  ORDER_TYPE_LABELS,
  FAILURE_TYPE_LABELS,
  ARRIVAL_METHOD_LABELS,
  APPROVAL_TYPE_LABELS
} from '../types';
import ServiceTimeTracker from './ServiceTimeTracker';

interface OrderDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  order: CorrectiveMaintenanceOrder;
}

export default function OrderDetailsModal({ isOpen, onClose, order }: OrderDetailsModalProps) {
  const [selectedImage, setSelectedImage] = useState<{ url: string; title: string } | null>(null);
  const totalParts = order.services?.reduce((sum, service) => sum + (service.parts?.length || 0), 0) || 0;
  const totalEvidence = order.services?.reduce((sum, service) => sum + (service.evidence?.length || 0), 0) || 0;



  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Detalles Completos - Orden #{order._id.slice(-8).toUpperCase()}
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="general" className="w-full">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="vehicle">Vehículo</TabsTrigger>
            <TabsTrigger value="services">
              Servicios {order.services && order.services.length > 0 && (
                <span className="ml-1 text-xs bg-blue-100 text-blue-800 px-1 rounded">
                  {order.services.length}
                </span>
              )}
            </TabsTrigger>
            <TabsTrigger value="time-tracking">
              <Activity className="w-4 h-4 mr-1" />
              Tiempo Real
            </TabsTrigger>
            <TabsTrigger value="diagnosis">
              Diagnóstico {order.diagnosisCompleted && (
                <span className="ml-1 text-xs bg-green-100 text-green-800 px-1 rounded">✓</span>
              )}
            </TabsTrigger>
            <TabsTrigger value="quotation">
              Cotización {order.quotation && (
                <span className="ml-1 text-xs bg-green-100 text-green-800 px-1 rounded">✓</span>
              )}
            </TabsTrigger>
          </TabsList>

          {/* General Information */}
          <TabsContent value="general" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Order Basic Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Info className="w-4 h-4" />
                    Información Básica
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-3 text-sm">
                    <div>
                      <p className="text-gray-500">ID de Orden:</p>
                      <p className="font-medium">{order._id}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Estado:</p>
                      <Badge className={getStatusColor(order.status, 'order')} variant="secondary">
                        {getStatusLabel(order.status, 'order')}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-gray-500">Tipo de Orden:</p>
                      <p className="font-medium">{ORDER_TYPE_LABELS[order.type]}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Tipo de Falla:</p>
                      <p className="font-medium">{FAILURE_TYPE_LABELS[order.failureType]}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Método de Llegada:</p>
                      <p className="font-medium">{ARRIVAL_METHOD_LABELS[order.arrivalMethod]}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Tipo de Aprobación:</p>
                      <p className="font-medium">{APPROVAL_TYPE_LABELS[order.approvalType]}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Puede Conducir:</p>
                      <p className="font-medium">{order.canVehicleDrive ? 'Sí' : 'No'}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Necesita Grúa:</p>
                      <p className="font-medium">{order.needsTowTruck ? 'Sí' : 'No'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Dates and IDs */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Fechas e Identificadores
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="space-y-3 text-sm">
                    <div>
                      <p className="text-gray-500">Stock ID:</p>
                      <p className="font-medium font-mono">{order.stockId}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Associate ID:</p>
                      <p className="font-medium font-mono">{order.associateId}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Workshop ID:</p>
                      <p className="font-medium font-mono">{order.workshopId}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Fecha de Creación:</p>
                      <p className="font-medium">
                        {format(new Date(order.createdAt), 'dd/MM/yyyy HH:mm:ss', { locale: es })}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-500">Última Actualización:</p>
                      <p className="font-medium">
                        {format(new Date(order.updatedAt), 'dd/MM/yyyy HH:mm:ss', { locale: es })}
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-500">SLA Target:</p>
                      <p className="font-medium">{order.slaTarget}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Customer Description */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-4 h-4" />
                  Descripción del Cliente
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm leading-relaxed">{order.customerDescription}</p>
                </div>
              </CardContent>
            </Card>

            {/* Workshop Information */}
            {order.workshop && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    Información del Taller
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Nombre:</p>
                      <p className="font-medium">{order.workshop.name}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">ID:</p>
                      <p className="font-medium font-mono">{order.workshop._id}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Order Progress */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-4 h-4" />
                  Progreso de la Orden
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className={`flex items-center space-x-2 ${
                    ['pending', 'diagnosed', 'quoted', 'approved', 'in-progress', 'waiting-for-parts', 'completed'].includes(order.status)
                      ? 'text-green-600' : 'text-gray-400'
                  }`}>
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm">Orden Creada</span>
                    <span className="text-xs text-gray-500">
                      {format(new Date(order.createdAt), 'dd/MM/yyyy HH:mm', { locale: es })}
                    </span>
                  </div>
                  <div className={`flex items-center space-x-2 ${
                    ['diagnosed', 'quoted', 'approved', 'in-progress', 'waiting-for-parts', 'completed'].includes(order.status)
                      ? 'text-green-600' : order.status === 'pending' ? 'text-yellow-600' : 'text-gray-400'
                  }`}>
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm">Diagnóstico</span>
                    {order.diagnosisCompleted && order.diagnosisDate ? (
                      <span className="text-xs text-gray-500">
                        {format(new Date(order.diagnosisDate), 'dd/MM/yyyy HH:mm', { locale: es })}
                      </span>
                    ) : order.status === 'pending' ? (
                      <span className="text-xs text-yellow-600">En progreso</span>
                    ) : (
                      <span className="text-xs text-gray-500">Pendiente</span>
                    )}
                  </div>

                  {/* Evidencias del Diagnóstico en Overview */}
                  {order.diagnosisCompleted && ((order.diagnosisEvidence?.photos && order.diagnosisEvidence.photos.length > 0) || 
                    (order.diagnosisEvidence?.videos && order.diagnosisEvidence.videos.length > 0)) && (
                    <div className="ml-6 mt-2 space-y-2">
                      <div className="flex items-center gap-2">
                        <FileText className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-600">
                          Evidencias ({(order.diagnosisEvidence?.photos?.length || 0) + (order.diagnosisEvidence?.videos?.length || 0)})
                        </span>
                      </div>
                      <div className="flex gap-1 flex-wrap">
                        {/* Mini thumbnails de fotos */}
                        {order.diagnosisEvidence?.photos?.slice(0, 4).map((photoUrl, index) => (
                          <div 
                            key={`mini-photo-${index}`}
                            className="w-8 h-8 rounded border border-gray-200 overflow-hidden cursor-pointer hover:shadow-md transition-shadow group"
                            onClick={() => setSelectedImage({ 
                              url: photoUrl, 
                              title: `Evidencia de Diagnóstico - Foto ${index + 1}` 
                            })}
                          >
                            <img
                              src={photoUrl}
                              alt={`Mini evidencia ${index + 1}`}
                              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-200"
                              onError={(e) => {
                                const target = e.target as HTMLImageElement;
                                target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9IiNGM0Y0RjYiLz48cGF0aCBkPSJNMTIgMTBIMjBWMjJIMTJWMTBaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMC41Ii8+PHBhdGggZD0iTTE0IDE0SDE4VjE4SDE0VjE0WiIgZmlsbD0iIzlDQTNBRiIvPjwvc3ZnPg==';
                              }}
                            />
                          </div>
                        ))}
                        
                        {/* Mini thumbnails de videos */}
                        {order.diagnosisEvidence?.videos?.slice(0, 2).map((videoUrl, index) => (
                          <div 
                            key={`mini-video-${index}`}
                            className="w-8 h-8 rounded border border-gray-200 overflow-hidden cursor-pointer hover:shadow-md transition-shadow bg-purple-50 flex items-center justify-center group"
                            onClick={() => window.open(videoUrl, '_blank')}
                          >
                            <Play className="w-3 h-3 text-purple-600 group-hover:text-purple-700 transition-colors" />
                          </div>
                        ))}
                        
                        {/* Indicador de más elementos */}
                        {((order.diagnosisEvidence?.photos?.length || 0) + (order.diagnosisEvidence?.videos?.length || 0)) > 6 && (
                          <div className="w-8 h-8 rounded border border-gray-200 bg-gray-100 flex items-center justify-center">
                            <span className="text-xs text-gray-500 font-medium">
                              +{((order.diagnosisEvidence?.photos?.length || 0) + (order.diagnosisEvidence?.videos?.length || 0)) - 6}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <div className={`flex items-center space-x-2 ${
                    ['quoted', 'approved', 'in-progress', 'waiting-for-parts', 'completed'].includes(order.status)
                      ? 'text-green-600' : 'text-gray-400'
                  }`}>
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm">Cotización</span>
                    {order.quotation && (
                      <span className="text-xs text-gray-500">
                        {format(new Date(order.quotation.createdAt), 'dd/MM/yyyy HH:mm', { locale: es })}
                      </span>
                    )}
                  </div>
                  <div className={`flex items-center space-x-2 ${
                    ['approved', 'in-progress', 'waiting-for-parts', 'completed'].includes(order.status)
                      ? 'text-green-600' : 'text-gray-400'
                  }`}>
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm">Servicios Aprobados</span>
                  </div>
                  <div className={`flex items-center space-x-2 ${
                    ['in-progress', 'waiting-for-parts', 'completed'].includes(order.status)
                      ? 'text-green-600' : 'text-gray-400'
                  }`}>
                    <Wrench className="h-4 w-4" />
                    <span className="text-sm">Trabajo Iniciado</span>
                  </div>

                  {/* Evidencias de Servicios/Trabajos en Modal */}
                  {order.services && order.services.length > 0 && (() => {
                    const servicesWithEvidence = order.services.filter(service => 
                      service.evidence && service.evidence.length > 0
                    );
                    const totalServiceEvidence = servicesWithEvidence.reduce((sum, service) => 
                      sum + (service.evidence?.length || 0), 0
                    );
                    
                    return servicesWithEvidence.length > 0 && (
                      <div className="ml-6 mt-2 space-y-3">
                        <div className="flex items-center gap-2">
                          <Wrench className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-600">
                            Evidencias de Trabajo ({totalServiceEvidence})
                          </span>
                        </div>
                        
                        {/* Mostrar evidencias por servicio */}
                        {servicesWithEvidence.map((service, serviceIndex) => (
                          <div key={service._id} className="space-y-1">
                            <div className="text-xs text-gray-500 font-medium">
                              {service.serviceName}
                            </div>
                            <div className="flex gap-1 flex-wrap">
                              {/* Evidencias del servicio */}
                              {service.evidence?.slice(0, 4).map((evidence, index) => (
                                <div key={`modal-service-${serviceIndex}-evidence-${index}`}>
                                  {evidence.type === 'photo' ? (
                                    <div 
                                      className="w-8 h-8 rounded border border-gray-200 overflow-hidden cursor-pointer hover:shadow-md transition-shadow group"
                                      onClick={() => setSelectedImage({ 
                                        url: evidence.url, 
                                        title: `${service.serviceName} - Evidencia ${index + 1}` 
                                      })}
                                    >
                                      <img
                                        src={evidence.url}
                                        alt={`Evidencia de ${service.serviceName} ${index + 1}`}
                                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-200"
                                        onError={(e) => {
                                          const target = e.target as HTMLImageElement;
                                          target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9IiNGM0Y0RjYiLz48cGF0aCBkPSJNMTIgMTBIMjBWMjJIMTJWMTBaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMC41Ii8+PHBhdGggZD0iTTE0IDE0SDE4VjE4SDE0VjE0WiIgZmlsbD0iIzlDQTNBRiIvPjwvc3ZnPg==';
                                        }}
                                      />
                                    </div>
                                  ) : (
                                    <div 
                                      className="w-8 h-8 rounded border border-gray-200 overflow-hidden cursor-pointer hover:shadow-md transition-shadow bg-purple-50 flex items-center justify-center group"
                                      onClick={() => window.open(evidence.url, '_blank')}
                                    >
                                      <svg className="w-3 h-3 text-purple-600 group-hover:text-purple-700 transition-colors" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M8 5v14l11-7z"/>
                                      </svg>
                                    </div>
                                  )}
                                </div>
                              ))}
                              
                              {/* Indicador de más evidencias en este servicio */}
                              {(service.evidence?.length || 0) > 4 && (
                                <div className="w-8 h-8 rounded border border-gray-200 bg-gray-100 flex items-center justify-center">
                                  <span className="text-xs text-gray-500 font-medium">
                                    +{(service.evidence?.length || 0) - 4}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    );
                  })()}
                  <div className={`flex items-center space-x-2 ${
                    order.status === 'completed' ? 'text-green-600' : 'text-gray-400'
                  }`}>
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm">Orden Completada</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Summary Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Wrench className="w-6 h-6 text-blue-600" />
                  </div>
                  <p className="text-2xl font-bold">{order.services?.length || 0}</p>
                  <p className="text-sm text-gray-500">Servicios</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <Package className="w-6 h-6 text-green-600" />
                  </div>
                  <p className="text-2xl font-bold">{totalParts}</p>
                  <p className="text-sm text-gray-500">Refacciones</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <FileText className="w-6 h-6 text-purple-600" />
                  </div>
                  <p className="text-2xl font-bold">{totalEvidence}</p>
                  <p className="text-sm text-gray-500">Evidencias</p>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <DollarSign className="w-6 h-6 text-yellow-600" />
                  </div>
                  <p className="text-2xl font-bold">
                    {order.totalEstimatedCost ? formatCurrency(order.totalEstimatedCost) : 'N/A'}
                  </p>
                  <p className="text-sm text-gray-500">Costo Total</p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Vehicle Information */}
          <TabsContent value="vehicle" className="space-y-6">
            {order.vehicle ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Car className="w-5 h-5" />
                    Información Completa del Vehículo
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">ID del Vehículo:</p>
                      <p className="font-medium font-mono">{order.vehicle._id}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Marca:</p>
                      <p className="font-medium">{order.vehicle.brand}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Modelo:</p>
                      <p className="font-medium">{order.vehicle.model}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Año:</p>
                      <p className="font-medium">{order.vehicle.year}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Número de Auto:</p>
                      <p className="font-medium">{order.vehicle.carNumber}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Placas:</p>
                      <p className="font-medium">{order.vehicle.carPlates?.plates || 'No disponible'}</p>
                    </div>
                    {order.vehicle.mileage && (
                      <div>
                        <p className="text-gray-500">Kilometraje:</p>
                        <p className="font-medium">{order.vehicle.mileage.toLocaleString()} km</p>
                      </div>
                    )}
                    {order.vehicle.color && (
                      <div>
                        <p className="text-gray-500">Color:</p>
                        <p className="font-medium">{order.vehicle.color}</p>
                      </div>
                    )}
                    {order.vehicle.vin && (
                      <div>
                        <p className="text-gray-500">VIN:</p>
                        <p className="font-medium font-mono">{order.vehicle.vin}</p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <Car className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Sin información del vehículo</h3>
                  <p className="text-gray-500">No se encontró información detallada del vehículo para esta orden.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Services Information */}
          <TabsContent value="services" className="space-y-6">
            {order.services && order.services.length > 0 ? (
              <div className="space-y-4">
                {order.services.map((service, index) => (
                  <Card key={service._id}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-lg">Servicio {index + 1}: {service.serviceName}</CardTitle>
                        <Badge className={getStatusColor(service.status, 'service')} variant="secondary">
                          {getStatusLabel(service.status, 'service')}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Service Basic Info */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">ID del Servicio:</p>
                          <p className="font-medium font-mono">{service._id}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Tipo de Servicio:</p>
                          <p className="font-medium">{service.serviceType}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Costo Estimado:</p>
                          <p className="font-medium">{formatCurrency(service.estimatedCost)}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Duración Estimada:</p>
                          <p className="font-medium">{formatDuration(service.estimatedDuration)}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">SLA Target:</p>
                          <p className="font-medium">{service.slaTarget}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Creado:</p>
                          <p className="font-medium">
                            {format(new Date(service.createdAt), 'dd/MM/yyyy HH:mm', { locale: es })}
                          </p>
                        </div>
                        <div>
                          <p className="text-gray-500">Actualizado:</p>
                          <p className="font-medium">
                            {format(new Date(service.updatedAt), 'dd/MM/yyyy HH:mm', { locale: es })}
                          </p>
                        </div>
                      </div>

                      {/* Service Description */}
                      <div>
                        <p className="text-gray-500 text-sm mb-2">Descripción:</p>
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <p className="text-sm">{service.description}</p>
                        </div>
                      </div>

                      {/* Parts */}
                      {service.parts && service.parts.length > 0 && (
                        <div>
                          <h4 className="font-medium mb-3 flex items-center gap-2">
                            <Package className="w-4 h-4" />
                            Refacciones ({service.parts.length})
                          </h4>
                          <div className="space-y-2">
                            {service.parts.map((part, partIndex) => (
                              <div key={partIndex} className="border rounded-lg p-3 bg-gray-50">
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                                  <div>
                                    <p className="text-gray-500">Nombre:</p>
                                    <p className="font-medium">{part.name}</p>
                                  </div>
                                  <div>
                                    <p className="text-gray-500">Cantidad:</p>
                                    <p className="font-medium">{part.quantity}</p>
                                  </div>
                                  <div>
                                    <p className="text-gray-500">Costo Total:</p>
                                    <p className="font-medium">{formatCurrency(part.totalCost)}</p>
                                  </div>
                                  {part.partNumber && (
                                    <div>
                                      <p className="text-gray-500">Número de Parte:</p>
                                      <p className="font-medium font-mono">{part.partNumber}</p>
                                    </div>
                                  )}
                                  {part.supplier && (
                                    <div>
                                      <p className="text-gray-500">Proveedor:</p>
                                      <p className="font-medium">{part.supplier}</p>
                                    </div>
                                  )}
                                  <div>
                                    <p className="text-gray-500">Disponibilidad:</p>
                                    <Badge variant={part.availability === 'available' ? 'default' : 'secondary'}>
                                      {part.availability === 'available' ? 'Disponible' : 'No disponible'}
                                    </Badge>
                                  </div>
                                  {part.eta && (
                                    <div>
                                      <p className="text-gray-500">ETA:</p>
                                      <p className="font-medium">{part.eta}</p>
                                    </div>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Evidence */}
                      {service.evidence && service.evidence.length > 0 && (
                        <div>
                          <h4 className="font-medium mb-3 flex items-center gap-2">
                            <FileText className="w-4 h-4" />
                            Evidencia ({service.evidence.length})
                          </h4>
                          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                            {service.evidence.map((evidence, evidenceIndex) => (
                              <div key={evidenceIndex} className="border rounded-lg overflow-hidden bg-gray-50">
                                {evidence.type === 'photo' ? (
                                  <div className="aspect-square bg-gray-100">
                                    <img
                                      src={evidence.url}
                                      alt={`Evidencia ${evidenceIndex + 1}`}
                                      className="w-full h-full object-cover cursor-pointer hover:opacity-90 transition-opacity"
                                      onClick={() => window.open(evidence.url, '_blank')}
                                      onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.style.display = 'none';
                                        const parent = target.parentElement;
                                        if (parent) {
                                          parent.innerHTML = `
                                            <div class="w-full h-full flex items-center justify-center bg-gray-200">
                                              <div class="text-center">
                                                <FileText class="w-8 h-8 mx-auto mb-2 text-gray-400" />
                                                <p class="text-xs text-gray-500">Error al cargar imagen</p>
                                              </div>
                                            </div>
                                          `;
                                        }
                                      }}
                                    />
                                  </div>
                                ) : (
                                  <div className="aspect-square bg-gray-100 flex items-center justify-center">
                                    <div className="text-center">
                                      <Play className="w-8 h-8 mx-auto mb-2 text-purple-600" />
                                      <p className="text-xs text-gray-600">Video</p>
                                      <button
                                        onClick={() => window.open(evidence.url, '_blank')}
                                        className="text-xs text-blue-600 hover:underline mt-1"
                                      >
                                        Ver video
                                      </button>
                                    </div>
                                  </div>
                                )}
                                <div className="p-2">
                                  <div className="text-xs space-y-1">
                                    <div className="flex items-center gap-1">
                                      {evidence.type === 'photo' ? (
                                        <FileText className="w-3 h-3 text-blue-600" />
                                      ) : (
                                        <Play className="w-3 h-3 text-purple-600" />
                                      )}
                                      <span className="font-medium">
                                        {evidence.type === 'photo' ? 'Foto' : 'Video'}
                                      </span>
                                    </div>
                                    {evidence.description && (
                                      <p
                                        className="text-gray-600 overflow-hidden"
                                        style={{
                                          display: '-webkit-box',
                                          WebkitLineClamp: 2,
                                          WebkitBoxOrient: 'vertical'
                                        }}
                                        title={evidence.description}
                                      >
                                        {evidence.description}
                                      </p>
                                    )}
                                    <p className="text-gray-500">
                                      {format(new Date(evidence.uploadedAt), 'dd/MM/yyyy HH:mm', { locale: es })}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <Wrench className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Sin servicios</h3>
                  <p className="text-gray-500">No se han definido servicios para esta orden aún.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Time Tracking Information */}
          <TabsContent value="time-tracking" className="space-y-6">
            {order.services && order.services.length > 0 ? (
              <div className="space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <Timer className="w-5 h-5 text-blue-600" />
                  <h3 className="text-lg font-medium">Seguimiento en Tiempo Real</h3>
                </div>

                <ServiceTimeTracker
                  services={order.services}
                  orderId={order._id}
                />

                {/* Service Status Overview */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Activity className="w-4 h-4" />
                      Estado Actual de Servicios
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      {order.services.map((service, index) => {
                        const getServiceIcon = (status: string) => {
                          switch (status) {
                            case 'not-started': return <Clock className="h-4 w-4 text-gray-500" />;
                            case 'in-progress': return <Play className="h-4 w-4 text-blue-500" />;
                            case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
                            case 'waiting-for-parts': return <Pause className="h-4 w-4 text-yellow-500" />;
                            default: return <Clock className="h-4 w-4 text-gray-500" />;
                          }
                        };

                        const getServiceStatusColor = (status: string) => {
                          switch (status) {
                            case 'not-started': return 'bg-gray-100 text-gray-800';
                            case 'in-progress': return 'bg-blue-100 text-blue-800';
                            case 'completed': return 'bg-green-100 text-green-800';
                            case 'waiting-for-parts': return 'bg-yellow-100 text-yellow-800';
                            default: return 'bg-gray-100 text-gray-800';
                          }
                        };

                        return (
                          <Card key={service._id} className="border-l-4 border-l-blue-200">
                            <CardContent className="pt-4">
                              <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    {getServiceIcon(service.status)}
                                    <span className="font-medium text-sm">{service.serviceName}</span>
                                  </div>
                                  <Badge variant="secondary" className={getServiceStatusColor(service.status)}>
                                    {getStatusLabel(service.status, 'service')}
                                  </Badge>
                                </div>

                                <div className="text-xs text-gray-600 space-y-1">
                                  <div className="flex justify-between">
                                    <span>Estimado:</span>
                                    <span>{formatDuration(service.estimatedDuration)}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span>Costo:</span>
                                    <span>{formatCurrency(service.estimatedCost)}</span>
                                  </div>
                                  {service.parts && service.parts.length > 0 && (
                                    <div className="flex justify-between">
                                      <span>Refacciones:</span>
                                      <span>{service.parts.length}</span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })}
                    </div>
                  </CardContent>
                </Card>

                {/* Inventory Flow Status */}
                {order.services.some(service => service.parts && service.parts.length > 0) && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Package className="w-4 h-4" />
                        Estado del Flujo de Inventarios
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {order.services
                          .filter(service => service.parts && service.parts.length > 0)
                          .map((service) => {
                            const availableParts = service.parts?.filter(part => part.availability === 'available').length || 0;
                            const unavailableParts = service.parts?.filter(part => part.availability === 'unavailable').length || 0;
                            const totalParts = service.parts?.length || 0;

                            return (
                              <div key={service._id} className="border rounded-lg p-3 bg-gray-50">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="font-medium text-sm">{service.serviceName}</span>
                                  <div className="flex items-center gap-2">
                                    {unavailableParts > 0 ? (
                                      <Badge className="bg-orange-100 text-orange-800">
                                        <AlertTriangle className="w-3 h-3 mr-1" />
                                        Requiere inventarios
                                      </Badge>
                                    ) : (
                                      <Badge className="bg-green-100 text-green-800">
                                        <CheckCircle className="w-3 h-3 mr-1" />
                                        Puede proceder
                                      </Badge>
                                    )}
                                  </div>
                                </div>
                                <div className="grid grid-cols-3 gap-4 text-xs">
                                  <div>
                                    <p className="text-gray-500">Total:</p>
                                    <p className="font-medium">{totalParts}</p>
                                  </div>
                                  <div>
                                    <p className="text-gray-500">Disponibles:</p>
                                    <p className="font-medium text-green-600">{availableParts}</p>
                                  </div>
                                  <div>
                                    <p className="text-gray-500">No disponibles:</p>
                                    <p className="font-medium text-red-600">{unavailableParts}</p>
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <Timer className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Sin servicios para rastrear</h3>
                  <p className="text-gray-500">No hay servicios definidos para esta orden aún.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Diagnosis Information */}
          <TabsContent value="diagnosis" className="space-y-6">
            {order.diagnosisCompleted && order.diagnosisNotes ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="w-5 h-5" />
                    Información del Diagnóstico
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Estado:</p>
                      <p className="font-medium text-green-600">Completado</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Completado el:</p>
                      <p className="font-medium">
                        {order.diagnosisDate && !isNaN(new Date(order.diagnosisDate).getTime())
                          ? format(new Date(order.diagnosisDate), 'dd/MM/yyyy HH:mm', { locale: es })
                          : 'No definido'
                        }
                      </p>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h4 className="font-medium mb-3">Notas del Diagnóstico</h4>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <p className="text-sm leading-relaxed">{order.diagnosisNotes}</p>
                    </div>
                  </div>

                  {((order.diagnosisEvidence?.photos && order.diagnosisEvidence.photos.length > 0) || 
                    (order.diagnosisEvidence?.videos && order.diagnosisEvidence.videos.length > 0)) && (
                    <div>
                      <h4 className="font-medium mb-3 flex items-center gap-2">
                        <FileText className="w-4 h-4" />
                        Evidencia del Diagnóstico ({(order.diagnosisEvidence?.photos?.length || 0) + (order.diagnosisEvidence?.videos?.length || 0)})
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                        {/* Mostrar fotos */}
                        {order.diagnosisEvidence?.photos?.map((photoUrl, index) => (
                          <div key={`photo-${index}`} className="border rounded-lg overflow-hidden bg-gray-50 hover:shadow-lg transition-all duration-200 group cursor-pointer">
                            <div 
                              className="aspect-square bg-gray-100 relative overflow-hidden"
                              onClick={() => setSelectedImage({ 
                                url: photoUrl, 
                                title: `Evidencia de Diagnóstico - Foto ${index + 1}` 
                              })}
                            >
                              <img
                                src={photoUrl}
                                alt={`Foto diagnóstico ${index + 1}`}
                                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5Y2EzYWYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5JbWFnZW4gbm8gZGlzcG9uaWJsZTwvdGV4dD48L3N2Zz4=';
                                }}
                              />
                              {/* Overlay de zoom */}
                              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white rounded-full p-3 shadow-xl">
                                  <svg className="w-5 h-5 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                                  </svg>
                                </div>
                              </div>
                            </div>
                            <div className="p-3">
                              <p className="text-xs text-gray-600 text-center font-medium">Foto {index + 1}</p>
                            </div>
                          </div>
                        )) || []}
                        
                        {/* Mostrar videos */}
                        {order.diagnosisEvidence?.videos?.map((videoUrl, index) => (
                          <div key={`video-${index}`} className="border rounded-lg overflow-hidden bg-gray-50 hover:shadow-lg transition-all duration-200 group">
                            <div 
                              className="aspect-square bg-gray-100 flex items-center justify-center cursor-pointer"
                              onClick={() => window.open(videoUrl, '_blank')}
                            >
                              <div className="text-center">
                                <Play className="w-8 h-8 mx-auto mb-2 text-purple-600 group-hover:text-purple-700 group-hover:scale-110 transition-all duration-200" />
                                <p className="text-xs text-gray-600 font-medium">Ver video</p>
                              </div>
                            </div>
                            <div className="p-3">
                              <p className="text-xs text-gray-600 text-center font-medium">Video {index + 1}</p>
                            </div>
                          </div>
                        )) || []}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6">
                <Card>
                  <CardContent className="p-8 text-center">
                    <FileText className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Sin diagnóstico</h3>
                    <p className="text-gray-500">El diagnóstico para esta orden aún no ha sido completado.</p>
                    {order.status === 'pending' && (
                      <p className="text-sm text-blue-600 mt-2">
                        La orden está pendiente de diagnóstico. Use el botón "Completar Diagnóstico" para agregar el diagnóstico.
                      </p>
                    )}
                  </CardContent>
                </Card>

                {/* Show current order information even without diagnosis */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Info className="w-5 h-5" />
                      Estado Actual de la Orden
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <span className="font-medium">Estado Actual:</span>
                        <Badge className={getStatusColor(order.status, 'order')} variant="secondary">
                          {getStatusLabel(order.status, 'order')}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Descripción del Cliente:</p>
                          <div className="bg-gray-50 p-3 rounded-lg mt-1">
                            <p className="text-sm">{order.customerDescription}</p>
                          </div>
                        </div>
                        <div>
                          <p className="text-gray-500">Información del Problema:</p>
                          <div className="space-y-2 mt-1">
                            <div className="flex justify-between">
                              <span>Tipo de Falla:</span>
                              <span className="font-medium">{FAILURE_TYPE_LABELS[order.failureType]}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Puede Conducir:</span>
                              <span className="font-medium">{order.canVehicleDrive ? 'Sí' : 'No'}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Necesita Grúa:</span>
                              <span className="font-medium">{order.needsTowTruck ? 'Sí' : 'No'}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>

          {/* Quotation Information */}
          <TabsContent value="quotation" className="space-y-6">
            {order.quotation ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="w-5 h-5" />
                    Información de la Cotización
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">ID de Cotización:</p>
                      <p className="font-medium font-mono">{order.quotation._id}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Número de Cotización:</p>
                      <p className="font-medium">{order.quotation.quotationNumber}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Estado:</p>
                      <Badge variant="secondary">{order.quotation.status}</Badge>
                    </div>
                    <div>
                      <p className="text-gray-500">Costo Total:</p>
                      <p className="font-medium text-lg">{formatCurrency(order.quotation.totalCost)}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Válida Hasta:</p>
                      <p className="font-medium">
                        {order.quotation.validUntil && !isNaN(new Date(order.quotation.validUntil).getTime()) 
                          ? format(new Date(order.quotation.validUntil), 'dd/MM/yyyy', { locale: es })
                          : 'No definida'
                        }
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-500">Creada:</p>
                      <p className="font-medium">
                        {order.quotation.createdAt && !isNaN(new Date(order.quotation.createdAt).getTime())
                          ? format(new Date(order.quotation.createdAt), 'dd/MM/yyyy HH:mm', { locale: es })
                          : 'No definida'
                        }
                      </p>
                    </div>
                  </div>

                  {order.quotation.services && order.quotation.services.length > 0 && (
                    <div>
                      <h4 className="font-medium mb-3">Servicios en la Cotización</h4>
                      <div className="space-y-3">
                        {order.quotation.services.map((service, index) => (
                          <div key={index} className="border rounded-lg p-3 bg-gray-50">
                            <div className="flex items-center justify-between mb-2">
                              <h5 className="font-medium">{service.serviceName}</h5>
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{formatCurrency(service.estimatedCost)}</span>
                                {service.isApproved !== undefined && (
                                  <Badge variant={service.isApproved ? 'default' : 'destructive'}>
                                    {service.isApproved ? 'Aprobado' : 'Rechazado'}
                                  </Badge>
                                )}
                              </div>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{service.description}</p>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                              <div>
                                <span className="text-gray-500">Refacciones:</span>
                                <span className="ml-1 font-medium">{formatCurrency(service.partsCost)}</span>
                              </div>
                              <div>
                                <span className="text-gray-500">Duración:</span>
                                <span className="ml-1 font-medium">{formatDuration(service.estimatedDuration)}</span>
                              </div>
                              <div>
                                <span className="text-gray-500">Service ID:</span>
                                <span className="ml-1 font-medium font-mono text-xs">{service.serviceId}</span>
                              </div>
                            </div>
                            {service.rejectionReason && (
                              <div className="mt-2 p-2 bg-red-50 rounded border-l-4 border-red-200">
                                <p className="text-sm text-red-700">
                                  <span className="font-medium">Razón de rechazo:</span> {service.rejectionReason}
                                </p>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <DollarSign className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Sin cotización</h3>
                  <p className="text-gray-500">La cotización para esta orden aún no ha sido creada.</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
      
      {/* Modal para ver imágenes ampliadas */}
      {selectedImage && (
        <ImageModal
          isOpen={selectedImage !== null}
          onClose={() => setSelectedImage(null)}
          imageUrl={selectedImage.url}
          title={selectedImage.title}
        />
      )}
    </Dialog>
  );
}
