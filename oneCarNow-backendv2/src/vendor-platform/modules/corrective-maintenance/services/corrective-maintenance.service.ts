/* eslint-disable brace-style */
/* eslint-disable max-params */
import { Types } from 'mongoose';
import { HttpException } from '@/vendor-platform/exceptions/HttpExceptions';
import {
  CorrectiveMaintenanceOrder,
  ICorrectiveMaintenanceOrder,
  CorrectiveMaintenanceStatus,
  CorrectiveMaintenanceType,
  VehicleArrivalMethod,
  FailureType,
} from '../models/corrective-maintenance-order.model';
import { CorrectiveService } from '../models/corrective-service.model';
import { Quotation } from '../models/quotation.model';
import { Workshop } from '../../workshop/models/workshops.model';
import { ServiceTypeVendorModel } from '../../serviceType/models/serviceType.model';
import { ScheduleService } from '../../workshop/services/schedule.service';
import StockVehicle from '@/models/StockVehicleSchema';
import Associate from '@/models/associateSchema';
import { BucketNameEnum, uploadFileAndReturnUrl } from '@/aws/s3';
import { logger } from '@/clean/lib/logger';
import { InventoryService } from './inventory.service';

export interface CreateCorrectiveMaintenanceOrderData {
  stockId: string;
  associateId: string;
  organizationId: string;
  workshopId: string;
  type: CorrectiveMaintenanceType;
  failureType: FailureType;
  arrivalMethod: VehicleArrivalMethod;
  customerDescription?: string;
  canVehicleDrive: boolean;
  needsTowTruck: boolean;
  approvalType: 'fleet' | 'customer';
}

export interface DiagnosisData {
  diagnosisNotes: string;
  diagnosisEvidence?: {
    photos: string[];
    videos: string[];
  };
  services: {
    serviceType: string;
    serviceName: string;
    description: string;
    estimatedCost: number;
    estimatedDuration: number;
    // laborCost: number; // Removed - not needed for vendor platform
    parts: {
      name: string;
      quantity: number;
      // unitCost: number; // Removed - not needed for vendor platform
      totalCost: number;
      supplier: string;
      estimatedArrival?: Date;
    }[];
  }[];
}

export class CorrectiveMaintenanceService {
  /**
   * Create a new corrective maintenance order
   */
  async createOrder(data: CreateCorrectiveMaintenanceOrderData): Promise<ICorrectiveMaintenanceOrder> {
    try {
      // Validate vehicle exists
      const vehicle = await StockVehicle.findOne({ _id: data.stockId });
      if (!vehicle) {
        throw HttpException.NotFound('Vehicle not found');
      }

      // Validate associate exists
      const associate = await Associate.findOne({ _id: data.associateId });
      if (!associate) {
        throw HttpException.NotFound('Associate not found');
      }

      // Validate workshop exists
      const workshop = await Workshop.findOne({ _id: data.workshopId });
      if (!workshop) {
        throw HttpException.NotFound('Workshop not found');
      }

      // Check if there's already an active corrective maintenance order
      const existingOrder = await CorrectiveMaintenanceOrder.findOne({
        stockId: data.stockId,
        status: {
          $in: [
            CorrectiveMaintenanceStatus.PENDING,
            CorrectiveMaintenanceStatus.DIAGNOSED,
            CorrectiveMaintenanceStatus.QUOTED,
            CorrectiveMaintenanceStatus.APPROVED,
            CorrectiveMaintenanceStatus.IN_PROGRESS,
            CorrectiveMaintenanceStatus.WAITING_FOR_PARTS,
          ],
        },
      });

      if (existingOrder) {
        throw HttpException.BadRequest('Vehicle already has an active corrective maintenance order');
      }

      // Calculate SLA target (default 72 hours for diagnosis)
      const slaTarget = new Date();
      slaTarget.setHours(slaTarget.getHours() + 72);

      // Create the order
      const order = new CorrectiveMaintenanceOrder({
        ...data,
        stockId: new Types.ObjectId(data.stockId),
        associateId: new Types.ObjectId(data.associateId),
        organizationId: new Types.ObjectId(data.organizationId),
        workshopId: new Types.ObjectId(data.workshopId),
        slaTarget,
        diagnosisEvidence: { photos: [], videos: [] },
        services: [],
        totalEstimatedCost: 0,
        totalEstimatedDuration: 0,
        pendingParts: [],
        beforePhotos: [],
        afterPhotos: [],
        completionEvidence: [],
        internalNotes: [],
        customerNotes: [],
      });

      await order.save();

      // Update vehicle's corrective maintenance history
      await this.updateVehicleHistory(data.stockId, {
        orderId: order._id,
        type: data.type,
        status: CorrectiveMaintenanceStatus.PENDING,
      });

      logger.info('Corrective maintenance order created', {
        orderId: order._id,
        stockId: data.stockId,
        type: data.type,
      });

      return order;
    } catch (error) {
      logger.error('Error creating corrective maintenance order', { error, data });
      throw error;
    }
  }

  /**
   * Complete diagnosis and create services
   */
  async completeDiagnosis(
    orderId: string,
    diagnosisData: DiagnosisData,
    files?: Express.Multer.File[]
  ): Promise<ICorrectiveMaintenanceOrder> {
    try {
      const order = await CorrectiveMaintenanceOrder.findById(orderId);
      if (!order) {
        throw HttpException.NotFound('Corrective maintenance order not found');
      }

      if (order.status !== CorrectiveMaintenanceStatus.PENDING) {
        throw HttpException.BadRequest('Order is not in pending status for diagnosis');
      }

      // Upload evidence files if provided
      let uploadedPhotos: string[] = [];
      let uploadedVideos: string[] = [];

      if (files && files.length > 0) {
        const photoFiles = files.filter((file) => file.mimetype.startsWith('image/'));
        const videoFiles = files.filter((file) => file.mimetype.startsWith('video/'));

        const photoUploads = await Promise.all(
          photoFiles.map((file) =>
            uploadFileAndReturnUrl(file, {
              route: `corrective-maintenance/${orderId}/diagnosis/photos/`,
              bucketName: BucketNameEnum.VENDOR_PLATFORM,
              isPublic: true,
            })
          )
        );
        uploadedPhotos = photoUploads.map((upload) => upload.url).filter((url): url is string => !!url);

        const videoUploads = await Promise.all(
          videoFiles.map((file) =>
            uploadFileAndReturnUrl(file, {
              route: `corrective-maintenance/${orderId}/diagnosis/videos/`,
              bucketName: BucketNameEnum.VENDOR_PLATFORM,
              isPublic: true,
            })
          )
        );
        uploadedVideos = videoUploads.map((upload) => upload.url).filter((url): url is string => !!url);
      }

      // Create services
      const serviceIds: Types.ObjectId[] = [];
      let totalEstimatedCost = 0;
      let totalEstimatedDuration = 0;

      for (const serviceData of diagnosisData.services) {
        const slaTarget = new Date();
        slaTarget.setHours(slaTarget.getHours() + serviceData.estimatedDuration);

        const service = new CorrectiveService({
          orderId: order._id,
          serviceType: serviceData.serviceType,
          serviceName: serviceData.serviceName,
          description: serviceData.description,
          estimatedCost: serviceData.estimatedCost,
          estimatedDuration: serviceData.estimatedDuration,
          // laborCost: serviceData.laborCost, // Removed
          parts: serviceData.parts.map((part) => ({
            ...part,
            totalCost: part.totalCost || 0, // Use provided totalCost directly
            isAvailable: !part.estimatedArrival || part.estimatedArrival <= new Date(),
          })),
          slaTarget,
          beforePhotos: [],
          progressPhotos: [],
          afterPhotos: [],
          videos: [],
          technicalNotes: [],
        });

        await service.save();
        serviceIds.push(service._id);
        totalEstimatedCost += serviceData.estimatedCost;
        totalEstimatedDuration += serviceData.estimatedDuration;
      }

      // Update order
      order.diagnosisCompleted = true;
      order.diagnosisDate = new Date();
      order.diagnosisNotes = diagnosisData.diagnosisNotes;
      order.diagnosisEvidence = {
        photos: [...(diagnosisData.diagnosisEvidence?.photos || []), ...uploadedPhotos],
        videos: [...(diagnosisData.diagnosisEvidence?.videos || []), ...uploadedVideos],
      };
      order.services = serviceIds;
      order.totalEstimatedCost = totalEstimatedCost;
      order.totalEstimatedDuration = totalEstimatedDuration;
      order.status = CorrectiveMaintenanceStatus.DIAGNOSED;

      await order.save();

      // Update vehicle history
      await this.updateVehicleHistory(order.stockId.toString(), {
        orderId: order._id,
        type: order.type,
        status: CorrectiveMaintenanceStatus.DIAGNOSED,
      });

      logger.info('Diagnosis completed for corrective maintenance order', {
        orderId: order._id,
        servicesCount: serviceIds.length,
        totalCost: totalEstimatedCost,
      });

      return order;
    } catch (error) {
      logger.error('Error completing diagnosis', { error, orderId });
      throw error;
    }
  }

  /**
   * Get corrective maintenance orders with filters
   */
  async getOrders(filters: {
    organizationId?: string;
    workshopId?: string;
    stockId?: string;
    associateId?: string;
    status?: CorrectiveMaintenanceStatus;
    type?: CorrectiveMaintenanceType;
    page?: number;
    limit?: number;
  }) {
    try {
      const query: any = {};

      // Convert string IDs to ObjectIds for proper comparison (all IDs are ObjectIds in database)
      if (filters.organizationId) {
        query.organizationId = new Types.ObjectId(filters.organizationId);
      }
      if (filters.workshopId) {
        query.workshopId = new Types.ObjectId(filters.workshopId);
      }
      if (filters.stockId) {
        query.stockId = new Types.ObjectId(filters.stockId);
      }
      if (filters.associateId) {
        query.associateId = new Types.ObjectId(filters.associateId);
      }
      if (filters.status) query.status = filters.status;
      if (filters.type) query.type = filters.type;

      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const skip = (page - 1) * limit;

      // Debug logging
      logger.info('Getting corrective maintenance orders', {
        filters,
        query: JSON.stringify(query, null, 2),
        page,
        limit,
        skip,
      });

      // Check total count first
      const totalCount = await CorrectiveMaintenanceOrder.countDocuments();
      const filteredCount = await CorrectiveMaintenanceOrder.countDocuments(query);

      logger.info('Order counts', {
        totalInDatabase: totalCount,
        matchingFilters: filteredCount,
      });

      const orders = await CorrectiveMaintenanceOrder.find(query)
        .populate('workshop', 'name location')
        .populate('organization', 'name')
        .populate('stockVehicle', 'brand model year carNumber carPlates color vin')
        .populate('associate', 'name email phone')
        .populate('services')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit);

      const total = await CorrectiveMaintenanceOrder.countDocuments(query);

      logger.info('Orders retrieved', {
        ordersFound: orders.length,
        totalMatching: total,
      });

      return {
        orders,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      logger.error('Error getting corrective maintenance orders', { error, filters });
      throw error;
    }
  }

  /**
   * Get a single corrective maintenance order by ID with quotation data
   */
  async getOrderById(orderId: string, organizationId: string): Promise<any> {
    try {
      const order = await CorrectiveMaintenanceOrder.findOne({
        _id: orderId,
        organizationId: new Types.ObjectId(organizationId),
      })
        .populate('workshop', 'name location')
        .populate('organization', 'name')
        .populate('stockVehicle', 'brand model year carNumber carPlates color vin')
        .populate('associate', 'name email phone')
        .populate('services');

      if (!order) {
        return null;
      }

      // Get the quotation associated with this order
      const quotation = await Quotation.findOne({
        orderId: order._id,
      }).sort({ createdAt: -1 }); // Get the most recent quotation

      // Return order with quotation data
      const orderData = order.toObject();
      return {
        ...orderData,
        quotation: quotation || null,
      };
    } catch (error) {
      logger.error('Error getting corrective maintenance order by ID', { error, orderId, organizationId });
      throw error;
    }
  }

  /**
   * Get quotation by order ID
   */
  async getQuotationByOrderId(orderId: string): Promise<any> {
    try {
      const quotation = await Quotation.findOne({
        orderId: new Types.ObjectId(orderId),
      }).sort({ createdAt: -1 }); // Get the most recent quotation

      return quotation;
    } catch (error) {
      logger.error('Error getting quotation by order ID', { error, orderId });
      throw error;
    }
  }

  /**
   * Start work on approved services
   */
  async startWork(orderId: string, organizationId: string): Promise<ICorrectiveMaintenanceOrder> {
    try {
      // Find the order and verify it belongs to the organization
      const order = await CorrectiveMaintenanceOrder.findOne({
        _id: orderId,
        organizationId: new Types.ObjectId(organizationId),
      }).populate('services');

      if (!order) {
        throw HttpException.NotFound('Corrective maintenance order not found');
      }

      // Verify order is in approved status
      if (order.status !== CorrectiveMaintenanceStatus.APPROVED) {
        throw HttpException.BadRequest(
          `Cannot start work on order with status: ${order.status}. Order must be approved first.`
        );
      }

      // Get the quotation to check which services are approved
      const quotation = await Quotation.findOne({
        orderId: order._id,
        status: { $in: ['approved', 'partially-approved'] },
      });

      if (!quotation) {
        throw HttpException.BadRequest('No approved quotation found for this order');
      }

      // Get approved services from quotation
      const approvedServiceIds = quotation.services
        .filter((service) => service.isApproved)
        .map((service) => service.serviceId);

      if (approvedServiceIds.length === 0) {
        throw HttpException.BadRequest('No approved services found to start work on');
      }

      // Update approved services to IN_PROGRESS status
      const currentTime = new Date();
      await CorrectiveService.updateMany(
        {
          _id: { $in: approvedServiceIds },
          orderId: order._id,
        },
        {
          $set: {
            status: 'in-progress',
            actualStartTime: currentTime,
          },
        }
      );

      // Check parts availability for approved services using inventory system
      const services = await CorrectiveService.find({
        _id: { $in: approvedServiceIds },
      });

      // Get vehicle information for compatibility checking
      const vehicle = await StockVehicle.findById(order.stockId);
      const vehicleInfo = vehicle
        ? {
            brand: vehicle.brand,
            model: vehicle.model,
            year: parseInt(vehicle.year),
          }
        : undefined;

      // Check inventory availability for each service
      const servicesWaitingForParts: any[] = [];
      for (const service of services) {
        if (service.parts && service.parts.length > 0) {
          const partsAvailability = await InventoryService.checkPartsAvailability(
            service.parts,
            organizationId,
            vehicleInfo
          );

          const allPartsAvailable = partsAvailability.every((part) => part.isAvailable);

          // Update service with inventory information
          const updatedParts = service.parts.map((part, index) => {
            const availability = partsAvailability[index];
            return {
              ...part,
              isAvailable: availability?.isAvailable || false,
              availableQuantity: availability?.availableQuantity || 0,
              needsOrdering: availability?.needsOrdering || false,
              inventoryStatus: availability?.isAvailable
                ? 'available'
                : availability?.availableQuantity > 0
                  ? 'low-stock'
                  : 'out-of-stock',
            };
          });

          // Update service parts with inventory information
          await CorrectiveService.findByIdAndUpdate(service._id, {
            parts: updatedParts,
            allPartsAvailable,
          });

          if (!allPartsAvailable) {
            servicesWaitingForParts.push(service);
          }

          // Reserve available parts
          const availableParts = service.parts.filter((_, index) => partsAvailability[index]?.isAvailable);
          if (availableParts.length > 0) {
            await InventoryService.reserveParts(availableParts, organizationId, orderId);
          }
        }
      }

      // Determine order status based on parts availability
      let newOrderStatus = CorrectiveMaintenanceStatus.IN_PROGRESS;
      if (servicesWaitingForParts.length > 0) {
        // If some services are waiting for parts, mark those services accordingly
        await CorrectiveService.updateMany(
          {
            _id: { $in: servicesWaitingForParts.map((s) => s._id) },
          },
          {
            $set: {
              status: 'waiting-for-parts',
            },
          }
        );

        // If ALL approved services are waiting for parts, mark order as waiting
        if (servicesWaitingForParts.length === approvedServiceIds.length) {
          newOrderStatus = CorrectiveMaintenanceStatus.WAITING_FOR_PARTS;
        }
      }

      // Update order status and start date
      order.status = newOrderStatus;
      order.startDate = currentTime;
      await order.save();

      // Update vehicle history
      await this.updateVehicleHistory(order.stockId.toString(), {
        orderId: order._id,
        type: order.type,
        status: newOrderStatus,
      });

      logger.info('Work started on corrective maintenance order', {
        orderId: order._id,
        approvedServicesCount: approvedServiceIds.length,
        servicesWaitingForParts: servicesWaitingForParts.length,
        orderStatus: newOrderStatus,
      });

      // Return updated order with populated services
      const updatedOrder = await CorrectiveMaintenanceOrder.findById(order._id)
        .populate('workshop', 'name location')
        .populate('organization', 'name')
        .populate('stockVehicle', 'brand model year carNumber carPlates color vin')
        .populate('associate', 'name email phone')
        .populate('services');

      return updatedOrder!;
    } catch (error) {
      logger.error('Error starting work on corrective maintenance order', { error, orderId, organizationId });
      throw error;
    }
  }

  /**
   * Update service progress with photos and notes
   */
  async updateServiceProgress(
    serviceId: string,
    organizationId: string,
    progressData: {
      status?: 'in-progress' | 'completed' | 'waiting-for-parts' | 'cancelled';
      notes?: string;
      progressPhotos?: string[];
      actualCost?: number;
      partsUsed?: {
        name: string;
        quantity: number;
        // unitCost: number; // Removed - not needed for vendor platform
        totalCost: number;
        supplier?: string;
      }[];
      qualityCheckPassed?: boolean;
      technicalNotes?: string[];
    },
    files?: Express.Multer.File[]
  ): Promise<any> {
    try {
      // Find the service and verify it exists
      const service = await CorrectiveService.findById(serviceId).populate('orderId');

      if (!service) {
        throw HttpException.NotFound('Corrective service not found');
      }

      // Verify the service belongs to an order from the correct organization
      const order = await CorrectiveMaintenanceOrder.findOne({
        _id: service.orderId,
        organizationId: new Types.ObjectId(organizationId),
      });

      if (!order) {
        throw HttpException.NotFound('Service does not belong to your organization');
      }

      // Verify service is in a state that allows progress updates
      if (service.status === 'completed' || service.status === 'cancelled') {
        throw HttpException.BadRequest(`Cannot update progress for service with status: ${service.status}`);
      }

      // Upload progress photos if provided
      let uploadedPhotos: string[] = [];
      if (files && files.length > 0) {
        const photoFiles = files.filter((file) => file.mimetype.startsWith('image/'));

        const photoUploads = await Promise.all(
          photoFiles.map((file) =>
            uploadFileAndReturnUrl(file, {
              route: `corrective-maintenance/${order._id}/services/${serviceId}/progress/`,
              bucketName: BucketNameEnum.VENDOR_PLATFORM,
              isPublic: true,
            })
          )
        );
        uploadedPhotos = photoUploads.map((upload) => upload.url).filter((url): url is string => !!url);
      }

      // Prepare update data
      const updateData: any = {};
      const currentTime = new Date();

      // Update status if provided
      if (progressData.status) {
        updateData.status = progressData.status;

        // Set completion time if service is being completed
        if (progressData.status === 'completed') {
          updateData.actualEndTime = currentTime;
          updateData.slaActual = currentTime;
          updateData.slaCompliance = currentTime <= service.slaTarget;
        }
      }

      // Update cost if provided
      if (progressData.actualCost !== undefined) {
        updateData.actualCost = progressData.actualCost;
      }

      // Update quality check if provided
      if (progressData.qualityCheckPassed !== undefined) {
        updateData.qualityCheckPassed = progressData.qualityCheckPassed;
      }

      // Add progress photos
      if (uploadedPhotos.length > 0 || progressData.progressPhotos) {
        const allProgressPhotos = [
          ...service.progressPhotos,
          ...uploadedPhotos,
          ...(progressData.progressPhotos || []),
        ];
        updateData.progressPhotos = allProgressPhotos;
      }

      // Add technical notes if provided
      if (progressData.technicalNotes && progressData.technicalNotes.length > 0) {
        updateData.technicalNotes = [...service.technicalNotes, ...progressData.technicalNotes];
      }

      // Add general notes if provided
      if (progressData.notes) {
        updateData.technicalNotes = [
          ...service.technicalNotes,
          `${currentTime.toISOString()}: ${progressData.notes}`,
        ];
      }

      // Update parts if provided
      if (progressData.partsUsed && progressData.partsUsed.length > 0) {
        const updatedParts = [...service.parts];

        // Update existing parts or add new ones
        progressData.partsUsed.forEach((usedPart) => {
          const existingPartIndex = updatedParts.findIndex((p) => p.name === usedPart.name);

          if (existingPartIndex >= 0) {
            // Update existing part
            updatedParts[existingPartIndex].quantity = usedPart.quantity;
            // updatedParts[existingPartIndex].unitCost = usedPart.unitCost; // Removed
            updatedParts[existingPartIndex].totalCost = usedPart.totalCost || 0;
            if (usedPart.supplier) {
              updatedParts[existingPartIndex].supplier = usedPart.supplier;
            }
          } else {
            // Add new part
            updatedParts.push({
              name: usedPart.name,
              quantity: usedPart.quantity,
              // unitCost: usedPart.unitCost, // Removed
              totalCost: usedPart.totalCost || 0,
              supplier: usedPart.supplier || 'Unknown',
              isAvailable: true,
            });
          }
        });

        updateData.parts = updatedParts;
        updateData.totalPartsCost = updatedParts.reduce((total, part) => total + part.totalCost, 0);
      }

      // Update the service
      await CorrectiveService.findByIdAndUpdate(serviceId, { $set: updateData }, { new: true });

      // Check if we need to update the order status
      await this.updateOrderStatusBasedOnServices(order._id);

      logger.info('Service progress updated', {
        serviceId,
        orderId: order._id,
        status: progressData.status,
        photosAdded: uploadedPhotos.length,
        notesAdded: progressData.notes ? 1 : 0,
      });

      // Return updated service with order info
      const serviceWithOrder = await CorrectiveService.findById(serviceId).populate({
        path: 'orderId',
        populate: [
          { path: 'workshop', select: 'name location' },
          { path: 'stockVehicle', select: 'brand model year carNumber carPlates color vin' },
          { path: 'associate', select: 'name email phone' },
        ],
      });

      return serviceWithOrder;
    } catch (error) {
      logger.error('Error updating service progress', { error, serviceId, organizationId });
      throw error;
    }
  }

  /**
   * Update order status based on all services status
   */
  private async updateOrderStatusBasedOnServices(orderId: Types.ObjectId): Promise<void> {
    try {
      const order = await CorrectiveMaintenanceOrder.findById(orderId);
      if (!order) return;

      const services = await CorrectiveService.find({ orderId });

      if (services.length === 0) return;

      const completedServices = services.filter((s) => s.status === 'completed');
      const inProgressServices = services.filter((s) => s.status === 'in-progress');
      const waitingForPartsServices = services.filter((s) => s.status === 'waiting-for-parts');
      const cancelledServices = services.filter((s) => s.status === 'cancelled');

      let newStatus = order.status;

      // If all services are completed
      if (completedServices.length === services.length) {
        newStatus = CorrectiveMaintenanceStatus.COMPLETED;
        order.completionDate = new Date();
      } else if (cancelledServices.length === services.length) {
        newStatus = CorrectiveMaintenanceStatus.CANCELLED;
      }
      // If all remaining services are waiting for parts
      else if (
        waitingForPartsServices.length > 0 &&
        inProgressServices.length === 0 &&
        completedServices.length < services.length
      ) {
        newStatus = CorrectiveMaintenanceStatus.WAITING_FOR_PARTS;
      } else if (inProgressServices.length > 0) {
        newStatus = CorrectiveMaintenanceStatus.IN_PROGRESS;
      }

      // Update order if status changed
      if (newStatus !== order.status) {
        order.status = newStatus;
        await order.save();

        // Update vehicle history
        await this.updateVehicleHistory(order.stockId.toString(), {
          orderId: order._id,
          type: order.type,
          status: newStatus,
        });

        logger.info('Order status updated based on services', {
          orderId,
          oldStatus: order.status,
          newStatus,
          completedServices: completedServices.length,
          totalServices: services.length,
        });
      }
    } catch (error) {
      logger.error('Error updating order status based on services', { error, orderId });
    }
  }

  /**
   * Complete corrective maintenance order
   */
  async completeOrder(
    orderId: string,
    organizationId: string,
    completionData: {
      completionNotes?: string;
      afterPhotos?: string[];
      finalInspectionPassed?: boolean;
      customerSatisfactionRating?: number; // 1-5 scale
      totalActualCost?: number;
      workQualityRating?: number; // 1-5 scale
      recommendationsForFuture?: string[];
    },
    files?: Express.Multer.File[]
  ): Promise<ICorrectiveMaintenanceOrder> {
    try {
      // Find the order and verify it belongs to the organization
      const order = await CorrectiveMaintenanceOrder.findOne({
        _id: orderId,
        organizationId: new Types.ObjectId(organizationId),
      }).populate('services');

      if (!order) {
        throw HttpException.NotFound('Corrective maintenance order not found');
      }

      // Verify order can be completed
      if (order.status === CorrectiveMaintenanceStatus.COMPLETED) {
        throw HttpException.BadRequest('Order is already completed');
      }

      if (order.status === CorrectiveMaintenanceStatus.CANCELLED) {
        throw HttpException.BadRequest('Cannot complete a cancelled order');
      }

      // Get all services for this order
      const services = await CorrectiveService.find({ orderId: order._id });

      // Check if all services are completed or cancelled
      const activeServices = services.filter((s) => s.status !== 'cancelled');
      const completedServices = activeServices.filter((s) => s.status === 'completed');
      const inProgressServices = activeServices.filter((s) => s.status === 'in-progress');
      const waitingServices = activeServices.filter((s) => s.status === 'waiting-for-parts');

      // Validate that all active services are completed
      if (completedServices.length !== activeServices.length) {
        const pendingCount = inProgressServices.length + waitingServices.length;
        throw HttpException.BadRequest(
          `Cannot complete order. ${pendingCount} service(s) are still pending completion. ` +
            `Completed: ${completedServices.length}, In Progress: ${inProgressServices.length}, ` +
            `Waiting for Parts: ${waitingServices.length}`
        );
      }

      // Upload completion photos if provided
      let uploadedPhotos: string[] = [];
      if (files && files.length > 0) {
        const photoFiles = files.filter((file) => file.mimetype.startsWith('image/'));

        const photoUploads = await Promise.all(
          photoFiles.map((file) =>
            uploadFileAndReturnUrl(file, {
              route: `corrective-maintenance/${order._id}/completion/`,
              bucketName: BucketNameEnum.VENDOR_PLATFORM,
              isPublic: true,
            })
          )
        );
        uploadedPhotos = photoUploads.map((upload) => upload.url).filter((url): url is string => !!url);
      }

      // Calculate total actual cost from all services
      const totalServicesCost = services.reduce((total, service) => {
        return total + (service.actualCost || service.estimatedCost || 0);
      }, 0);

      const finalActualCost = completionData.totalActualCost || totalServicesCost;

      // Prepare completion data
      const currentTime = new Date();
      const completionUpdate: any = {
        status: CorrectiveMaintenanceStatus.COMPLETED,
        completionDate: currentTime,
        totalActualCost: finalActualCost,
      };

      // Add completion notes if provided
      if (completionData.completionNotes) {
        completionUpdate.completionNotes = completionData.completionNotes;
      }

      // Add after photos
      if (uploadedPhotos.length > 0 || completionData.afterPhotos) {
        const allAfterPhotos = [
          ...(order.afterPhotos || []),
          ...uploadedPhotos,
          ...(completionData.afterPhotos || []),
        ];
        completionUpdate.afterPhotos = allAfterPhotos;
      }

      // Add quality ratings and inspection results
      if (completionData.finalInspectionPassed !== undefined) {
        completionUpdate.finalInspectionPassed = completionData.finalInspectionPassed;
      }

      if (completionData.customerSatisfactionRating !== undefined) {
        completionUpdate.customerSatisfactionRating = completionData.customerSatisfactionRating;
      }

      if (completionData.workQualityRating !== undefined) {
        completionUpdate.workQualityRating = completionData.workQualityRating;
      }

      if (completionData.recommendationsForFuture && completionData.recommendationsForFuture.length > 0) {
        completionUpdate.recommendationsForFuture = completionData.recommendationsForFuture;
      }

      // Calculate total duration
      if (order.startDate) {
        const totalDurationHours = Math.round(
          (currentTime.getTime() - order.startDate.getTime()) / (1000 * 60 * 60)
        );
        completionUpdate.totalDurationHours = totalDurationHours;
      }

      // Calculate SLA compliance for the entire order
      const quotation = await Quotation.findOne({ orderId: order._id });
      if (quotation && quotation.overallSLA) {
        const slaTargetDate = new Date(order.createdAt.getTime() + quotation.overallSLA * 60 * 60 * 1000);
        completionUpdate.slaCompliance = currentTime <= slaTargetDate;
        completionUpdate.slaTarget = slaTargetDate;
        completionUpdate.slaActual = currentTime;
      }

      // Update the order
      const updatedOrder = await CorrectiveMaintenanceOrder.findByIdAndUpdate(
        orderId,
        { $set: completionUpdate },
        { new: true }
      ).populate([
        { path: 'workshop', select: 'name location contact' },
        { path: 'organization', select: 'name' },
        { path: 'stockVehicle', select: 'brand model year carNumber carPlates color vin' },
        { path: 'associate', select: 'name email phone' },
        { path: 'services' },
      ]);

      // Update vehicle maintenance history with completion
      await this.updateVehicleHistory(order.stockId.toString(), {
        orderId: order._id,
        type: order.type,
        status: CorrectiveMaintenanceStatus.COMPLETED,
      });

      // Log maintenance completion details for audit trail

      logger.info('Corrective maintenance order completed', {
        orderId: order._id,
        totalCost: finalActualCost,
        servicesCompleted: completedServices.length,
        totalServices: services.length,
        slaCompliance: completionUpdate.slaCompliance,
        duration: completionUpdate.totalDurationHours,
      });

      return updatedOrder!;
    } catch (error) {
      logger.error('Error completing corrective maintenance order', { error, orderId, organizationId });
      throw error;
    }
  }

  /**
   * Add notes to a corrective maintenance order without changing its status
   */
  async addOrderNotes(
    orderId: string,
    notes: string,
    isInternal: boolean = true
  ): Promise<ICorrectiveMaintenanceOrder> {
    try {
      const order = await CorrectiveMaintenanceOrder.findById(orderId);

      if (!order) {
        throw HttpException.NotFound('Corrective maintenance order not found');
      }

      const timestampedNote = `${new Date().toISOString()}: ${notes}`;

      if (isInternal) {
        order.internalNotes.push(timestampedNote);
      } else {
        order.customerNotes.push(timestampedNote);
      }

      await order.save();

      logger.info('Notes added to corrective maintenance order', {
        orderId: order._id,
        isInternal,
        noteLength: notes.length,
      });

      return order;
    } catch (error) {
      logger.error('Error adding notes to corrective maintenance order', { error, orderId });
      throw error;
    }
  }

  /**
   * Create appointment for corrective maintenance
   */
  async createCorrectiveMaintenanceAppointment(
    orderId: string,
    appointmentData: {
      startTime: Date;
      endTime: Date;
      serviceTypeId: string; // Now required - must be provided
      failureDescription?: string;
      urgencyLevel?: 'low' | 'medium' | 'high' | 'critical';
    }
  ) {
    try {
      const order = await CorrectiveMaintenanceOrder.findById(orderId);
      if (!order) {
        throw HttpException.NotFound('Corrective maintenance order not found');
      }

      // Validate that the service type exists and is for corrective maintenance
      const serviceType = await ServiceTypeVendorModel.findOne({
        _id: appointmentData.serviceTypeId,
        organization: order.organizationId,
        maintenanceType: 'corrective',
        isActive: true,
      });

      if (!serviceType) {
        throw HttpException.NotFound('Corrective maintenance service type not found or inactive');
      }

      // Create appointment using ScheduleService
      const appointment = await ScheduleService.createCorrectiveMaintenanceAppointment(
        order.workshopId.toString(),
        appointmentData.startTime.toISOString(),
        appointmentData.endTime.toISOString(),
        {
          associateId: order.associateId.toString(),
          stockId: order.stockId.toString(),
          registeredKm: 0, // Will be updated when vehicle arrives
          correctiveMaintenanceOrderId: order._id.toString(),
          failureDescription: appointmentData.failureDescription,
          urgencyLevel: appointmentData.urgencyLevel || 'medium',
        },
        serviceType._id.toString()
      );

      logger.info('Corrective maintenance appointment created', {
        appointmentId: appointment._id,
        orderId: order._id,
      });

      return appointment;
    } catch (error) {
      logger.error('Error creating corrective maintenance appointment', { error, orderId });
      throw error;
    }
  }

  /**
   * Update vehicle's corrective maintenance history
   */
  private async updateVehicleHistory(
    stockId: string,
    updateData: {
      orderId: Types.ObjectId;
      type: CorrectiveMaintenanceType;
      status: CorrectiveMaintenanceStatus;
    }
  ) {
    try {
      const vehicle = await StockVehicle.findById(stockId);
      if (!vehicle) return;

      const existingEntry = vehicle.correctiveMaintenanceHistory.find(
        (entry) => entry.orderId.toString() === updateData.orderId.toString()
      );

      if (existingEntry) {
        existingEntry.status = updateData.status as any;
        if (updateData.status === CorrectiveMaintenanceStatus.COMPLETED) {
          existingEntry.completedAt = new Date();
        }
      } else {
        vehicle.correctiveMaintenanceHistory.push({
          _id: new Types.ObjectId(),
          orderId: updateData.orderId,
          type: updateData.type,
          status: updateData.status as any,
          totalCost: 0,
          totalDuration: 0,
          createdAt: new Date(),
        });
      }

      await vehicle.save();
    } catch (error) {
      logger.error('Error updating vehicle corrective maintenance history', {
        error,
        stockId,
        orderId: updateData.orderId,
      });
    }
  }
}

export const correctiveMaintenanceService = new CorrectiveMaintenanceService();
