'use client';

import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Quotation } from '../types';
import { escalateToFleet, sendCustomerReminder, checkAndAutoEscalate } from '../_actions/approvalEscalation';

interface UseApprovalEscalationProps {
  quotations: Quotation[];
  onQuotationUpdate: (quotationId: string, updates: Partial<Quotation>) => void;
  autoEscalateEnabled?: boolean;
  checkIntervalMinutes?: number;
}

export function useApprovalEscalation({
  quotations,
  onQuotationUpdate,
  autoEscalateEnabled = true,
  checkIntervalMinutes = 5
}: UseApprovalEscalationProps) {
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState<string | null>(null);
  const [lastCheckTime, setLastCheckTime] = useState<Date>(new Date());

  // Filter quotations that are pending customer approval
  const pendingCustomerQuotations = quotations.filter(q => 
    q.status === 'pending-customer' && 
    q.approvalType === 'customer' &&
    !q.escalatedToFleetAt
  );

  // Filter quotations that are overdue for customer response
  const overdueCustomerQuotations = pendingCustomerQuotations.filter(q => {
    if (!q.customerApprovalDeadline) return false;
    return new Date(q.customerApprovalDeadline) < new Date();
  });

  // Filter quotations approaching deadline (within 2 hours)
  const approachingDeadlineQuotations = pendingCustomerQuotations.filter(q => {
    if (!q.customerApprovalDeadline) return false;
    const deadline = new Date(q.customerApprovalDeadline);
    const now = new Date();
    const twoHoursFromNow = new Date(now.getTime() + (2 * 60 * 60 * 1000));
    return deadline <= twoHoursFromNow && deadline > now;
  });

  const calculateTimeRemaining = useCallback((deadline: string): string => {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diffMs = deadlineDate.getTime() - now.getTime();
    
    if (diffMs <= 0) return 'Vencido';
    
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 24) {
      const days = Math.floor(diffHours / 24);
      return `${days} día${days > 1 ? 's' : ''}`;
    } else if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes}m`;
    } else {
      return `${diffMinutes}m`;
    }
  }, []);

  const escalateQuotationToFleet = useCallback(async (quotationId: string) => {
    if (isProcessing === quotationId) return;
    
    setIsProcessing(quotationId);
    try {
      const response = await escalateToFleet(quotationId);
      
      if (response.success) {
        toast({
          title: 'Escalación Automática',
          description: 'Cotización escalada a fleet por tiempo vencido',
        });
        
        onQuotationUpdate(quotationId, {
          status: 'escalated-to-fleet',
          approvalType: 'fleet',
          escalatedToFleetAt: new Date().toISOString()
        });
        
        return true;
      } else {
        console.error('Error escalating quotation:', response.message);
        return false;
      }
    } catch (error) {
      console.error('Error escalating quotation:', error);
      return false;
    } finally {
      setIsProcessing(null);
    }
  }, [isProcessing, toast, onQuotationUpdate]);

  const sendReminderToCustomer = useCallback(async (quotationId: string) => {
    if (isProcessing === quotationId) return;
    
    setIsProcessing(quotationId);
    try {
      const response = await sendCustomerReminder(quotationId);
      
      if (response.success) {
        toast({
          title: 'Recordatorio Enviado',
          description: 'Se envió un recordatorio al cliente',
        });
        return true;
      } else {
        console.error('Error sending reminder:', response.message);
        return false;
      }
    } catch (error) {
      console.error('Error sending reminder:', error);
      return false;
    } finally {
      setIsProcessing(null);
    }
  }, [isProcessing, toast]);

  // Auto-escalate overdue quotations
  const processOverdueQuotations = useCallback(async () => {
    if (!autoEscalateEnabled || overdueCustomerQuotations.length === 0) return;

    console.log(`Processing ${overdueCustomerQuotations.length} overdue quotations`);
    
    for (const quotation of overdueCustomerQuotations) {
      await escalateQuotationToFleet(quotation._id);
    }
  }, [autoEscalateEnabled, overdueCustomerQuotations, escalateQuotationToFleet]);

  // Send reminders for quotations approaching deadline
  const processApproachingDeadlines = useCallback(async () => {
    if (!autoEscalateEnabled || approachingDeadlineQuotations.length === 0) return;

    console.log(`Sending reminders for ${approachingDeadlineQuotations.length} quotations approaching deadline`);
    
    for (const quotation of approachingDeadlineQuotations) {
      // Only send reminder if we haven't sent one recently
      const lastReminder = quotation.customerNotificationSent;
      if (!lastReminder) {
        await sendReminderToCustomer(quotation._id);
      }
    }
  }, [autoEscalateEnabled, approachingDeadlineQuotations, sendReminderToCustomer]);

  // Periodic check for escalations and reminders
  useEffect(() => {
    if (!autoEscalateEnabled) return;

    const interval = setInterval(async () => {
      const now = new Date();
      const timeSinceLastCheck = now.getTime() - lastCheckTime.getTime();
      const checkIntervalMs = checkIntervalMinutes * 60 * 1000;

      if (timeSinceLastCheck >= checkIntervalMs) {
        console.log('Running approval escalation check...');
        
        // Process overdue quotations first
        await processOverdueQuotations();
        
        // Then send reminders for approaching deadlines
        await processApproachingDeadlines();
        
        setLastCheckTime(now);
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [
    autoEscalateEnabled,
    checkIntervalMinutes,
    lastCheckTime,
    processOverdueQuotations,
    processApproachingDeadlines
  ]);

  // Manual escalation function
  const manualEscalateToFleet = useCallback(async (quotationId: string) => {
    return await escalateQuotationToFleet(quotationId);
  }, [escalateQuotationToFleet]);

  // Manual reminder function
  const manualSendReminder = useCallback(async (quotationId: string) => {
    return await sendReminderToCustomer(quotationId);
  }, [sendReminderToCustomer]);

  return {
    // State
    isProcessing,
    pendingCustomerQuotations,
    overdueCustomerQuotations,
    approachingDeadlineQuotations,
    
    // Functions
    calculateTimeRemaining,
    manualEscalateToFleet,
    manualSendReminder,
    
    // Statistics
    stats: {
      totalPending: pendingCustomerQuotations.length,
      totalOverdue: overdueCustomerQuotations.length,
      totalApproachingDeadline: approachingDeadlineQuotations.length,
      lastCheckTime
    }
  };
}
