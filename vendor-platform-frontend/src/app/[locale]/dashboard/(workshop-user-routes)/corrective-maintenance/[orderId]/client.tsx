'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { ArrowLeft, FileText, Wrench, CheckCircle, Clock, AlertTriangle, Package, History, Car } from 'lucide-react';
import {
  CorrectiveMaintenanceOrder,
  getStatusLabel,
  getStatusColor,
  formatCurrency,
  formatDuration,
  ORDER_TYPE_LABELS,
  FAILURE_TYPE_LABELS,
  ARRIVAL_METHOD_LABELS,
  APPROVAL_TYPE_LABELS,
  canStartWork
} from '../types';
import DiagnosisModal from '../_components/DiagnosisModal';
import QuotationModal from '../_components/QuotationModal';
import ApprovalModal from '../_components/ApprovalModal';
import ServiceProgressModal from '../_components/ServiceProgressModal';
import PartsManagementModal from '../_components/PartsManagementModal';
import VehicleHistoryModal from '../_components/VehicleHistoryModal';
import ImageModal from '../_components/ImageModal';
import StartWorkConfirmationModal from '../_components/StartWorkConfirmationModal';
import PartsStatusIndicator from '../_components/PartsStatusIndicator';
import OrderStatusAlert from '../_components/OrderStatusAlert';
import WaitingForPartsActions from '../_components/WaitingForPartsActions';
import OrderDetailsModal from '../_components/OrderDetailsModal';
import { startWork } from '../_actions/startWork';

interface OrderDetailClientProps {
  order: CorrectiveMaintenanceOrder;
}

export default function OrderDetailClient({ order }: OrderDetailClientProps) {
  const router = useRouter();
  const { toast } = useToast();


  const [isDiagnosisModalOpen, setIsDiagnosisModalOpen] = useState(false);
  const [isQuotationModalOpen, setIsQuotationModalOpen] = useState(false);
  const [isApprovalModalOpen, setIsApprovalModalOpen] = useState(false);
  const [isServiceProgressModalOpen, setIsServiceProgressModalOpen] = useState(false);
  const [isPartsManagementModalOpen, setIsPartsManagementModalOpen] = useState(false);
  const [isVehicleHistoryModalOpen, setIsVehicleHistoryModalOpen] = useState(false);
  const [isStartWorkConfirmationModalOpen, setIsStartWorkConfirmationModalOpen] = useState(false);
  const [isOrderDetailsModalOpen, setIsOrderDetailsModalOpen] = useState(false);
  const [isCloseOrderModalOpen, setIsCloseOrderModalOpen] = useState(false);
  const [selectedServiceId, setSelectedServiceId] = useState<string | null>(null);
  const [selectedServiceName, setSelectedServiceName] = useState<string>('');
  const [selectedServiceType, setSelectedServiceType] = useState<string>('');
  const [isStartingWork, setIsStartingWork] = useState(false);
  const [selectedImage, setSelectedImage] = useState<{ url: string; title: string } | null>(null);

  const handleBack = () => {
    router.push('/dashboard/corrective-maintenance');
  };

  const handleDiagnosisComplete = () => {
    setIsDiagnosisModalOpen(false);
    router.refresh();
  };

  const handleQuotationCreated = () => {
    setIsQuotationModalOpen(false);
    router.refresh();
  };

  const handleOrderClosed = () => {
    setIsCloseOrderModalOpen(false);
    router.refresh();
  };

  // Check if all services are completed
  const allServicesCompleted = order.services && order.services.length > 0 &&
    order.services.every(service => service.status === 'completed');

  // Check if order can be closed (all services completed and order is in-progress)
  const canCloseOrder = allServicesCompleted &&
    (order.status === 'in-progress' || order.status === 'waiting-for-parts');

  const handleApprovalProcessed = () => {
    setIsApprovalModalOpen(false);
    router.refresh();
  };

  const handleServiceProgressUpdated = () => {
    console.log('🔄 Service progress updated, refreshing page...');
    setIsServiceProgressModalOpen(false);
    setSelectedServiceId(null);

    // Show a loading toast while refreshing
    toast({
      title: '🔄 Actualizando...',
      description: 'Refrescando datos del servicio',
    });

    // Refresh the page to get updated data
    router.refresh();
  };

  const handleUpdateServiceProgress = (serviceId: string) => {
    const service = order.services?.find(s => s._id === serviceId);
    setSelectedServiceId(serviceId);
    setSelectedServiceName(service?.serviceName || '');
    setSelectedServiceType(service?.serviceType || '');
    setIsServiceProgressModalOpen(true);
  };



  const handleManageParts = (serviceId: string) => {
    const service = order.services?.find(s => s._id === serviceId);

    if (!service) {
      toast({
        title: 'Error',
        description: 'No se encontró el servicio especificado.',
        variant: 'destructive',
      });
      return;
    }

    setSelectedServiceId(serviceId);
    setSelectedServiceName(service.serviceName || '');
    setSelectedServiceType(service.serviceType || '');
    setIsPartsManagementModalOpen(true);
  };

  const handlePartsManagementUpdated = () => {
    setIsPartsManagementModalOpen(false);
    setSelectedServiceId(null);
    setSelectedServiceName('');
    setSelectedServiceType('');
    router.refresh();
  };

  // Convert ServicePart to Part format expected by PartsManagementModal
  const convertServicePartsToModalParts = (serviceParts: any[]) => {
    return serviceParts.map(part => ({
      _id: part._id,
      partName: part.name,
      partNumber: part.partNumber,
      quantity: part.quantity,
      unitCost: part.unitCost,
      supplier: part.supplier,
      estimatedArrival: part.eta,
      status: mapAvailabilityToStatus(part.availability),
      notes: part.notes || '',
    }));
  };

  // Map availability to status for the modal
  const mapAvailabilityToStatus = (availability: string): 'pending' | 'ordered' | 'in-transit' | 'arrived' | 'installed' => {
    switch (availability) {
      case 'available': return 'arrived';
      case 'pending': return 'pending';
      case 'unavailable': return 'pending';
      default: return 'pending';
    }
  };

  const handleVehicleHistoryClose = () => {
    setIsVehicleHistoryModalOpen(false);
  };

  const handleStartWorkClick = () => {
    // Check if there are services with parts
    const servicesWithParts = order.services?.filter(service =>
      service.parts && service.parts.length > 0
    ) || [];

    const hasPendingParts = servicesWithParts.some(service =>
      service.parts.some(part => part.availability === 'pending')
    );

    // If there are pending parts, show confirmation modal
    if (hasPendingParts || servicesWithParts.length > 0) {
      setIsStartWorkConfirmationModalOpen(true);
    } else {
      // If no parts involved, start work directly
      handleStartWorkDirect();
    }
  };

  const handleStartWorkDirect = async () => {
    setIsStartingWork(true);
    try {
      const response = await startWork(order._id);

      if (response.success) {
        toast({
          title: 'Éxito',
          description: 'Trabajo iniciado exitosamente',
        });
        router.refresh();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Error al iniciar el trabajo',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error starting work:', error);
      toast({
        title: 'Error',
        description: 'Error inesperado al iniciar el trabajo',
        variant: 'destructive',
      });
    } finally {
      setIsStartingWork(false);
    }
  };

  const handleStartWorkConfirmed = () => {
    setIsStartWorkConfirmationModalOpen(false);
    router.refresh();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver
          </Button>
          <div>
            <h1 className="text-2xl font-bold">
              Orden #{order._id.slice(-8).toUpperCase()}
            </h1>
            <div className="space-y-1">
              <p className="text-gray-600">
                {ORDER_TYPE_LABELS[order.type]} • {FAILURE_TYPE_LABELS[order.failureType]}
              </p>
              {order.vehicle && (
                <p className="text-gray-600 flex items-center gap-2">
                  <Car className="h-4 w-4" />
                  {order.vehicle.brand} {order.vehicle.model} {order.vehicle.year}
                  <span className="text-gray-400">•</span>
                  {order.vehicle.carPlates?.plates}
                </p>
              )}
            </div>
          </div>
        </div>
        <Badge className={getStatusColor(order.status, 'order')} variant="secondary">
          {getStatusLabel(order.status, 'order')}
        </Badge>
      </div>

      {/* Order Status Alert */}
      <OrderStatusAlert
        order={order}
        onStartWork={handleStartWorkClick}
        onManageParts={() => {
          // Open parts management for the first service with parts
          const serviceWithParts = order.services?.find(s => s.parts && s.parts.length > 0);
          if (serviceWithParts) {
            handleManageParts(serviceWithParts._id);
          }
        }}
      />

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-2">


        {order.status === 'pending' && (
          <Button onClick={() => setIsDiagnosisModalOpen(true)}>
            <FileText className="h-4 w-4 mr-2" />
            Completar Diagnóstico
          </Button>
        )}

        {order.status === 'diagnosed' && (
          <Button onClick={() => setIsQuotationModalOpen(true)}>
            <FileText className="h-4 w-4 mr-2" />
            Crear Cotización
          </Button>
        )}

        {order.status === 'quoted' && order.quotation && (
          <Button onClick={() => setIsApprovalModalOpen(true)}>
            <CheckCircle className="h-4 w-4 mr-2" />
            Procesar Aprobación
          </Button>
        )}

        {order.status === 'quoted' && !order.quotation && (
          <Button variant="outline" disabled>
            <AlertTriangle className="h-4 w-4 mr-2" />
            Cotización no disponible
          </Button>
        )}

        {canStartWork(order.status) && (
          <Button onClick={handleStartWorkClick} disabled={isStartingWork}>
            <Wrench className="h-4 w-4 mr-2" />
            {isStartingWork ? 'Iniciando...' : 'Iniciar Trabajo'}
          </Button>
        )}

        {order.status === 'waiting-for-parts' && (
          <Button
            variant="outline"
            onClick={() => {
              const serviceWithParts = order.services?.find(s => s.parts && s.parts.length > 0);

              if (serviceWithParts) {
                handleManageParts(serviceWithParts._id);
              } else {
                toast({
                  title: 'Sin refacciones',
                  description: 'No se encontraron servicios con refacciones para gestionar.',
                  variant: 'destructive',
                });
              }
            }}
          >
            <Package className="h-4 w-4 mr-2" />
            Gestionar Refacciones
          </Button>
        )}

        {/* Close Order Button - Show when all services are completed */}
        {canCloseOrder && (
          <Button
            onClick={() => setIsCloseOrderModalOpen(true)}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Cerrar Orden
          </Button>
        )}

        {/* Vehicle History Button */}
        <Button
          variant="outline"
          onClick={() => setIsVehicleHistoryModalOpen(true)}
        >
          <History className="h-4 w-4 mr-2" />
          Historial del Vehículo
        </Button>

        {/* Order Details Button */}
        <Button
          variant="outline"
          onClick={() => setIsOrderDetailsModalOpen(true)}
        >
          <FileText className="h-4 w-4 mr-2" />
          Ver Todos los Detalles
        </Button>


      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Order Information */}
          <Card>
            <CardHeader>
              <CardTitle>Información de la Orden</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-500">Vehículo</p>
                  {order.vehicle ? (
                    <div className="space-y-1">
                      <p className="text-sm font-medium">
                        {order.vehicle.brand} {order.vehicle.model} {order.vehicle.year}
                      </p>
                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <span>#{order.vehicle.carNumber}</span>
                        <span>•</span>
                        <span>{order.vehicle.carPlates?.plates}</span>
                        {order.vehicle.mileage && (
                          <>
                            <span>•</span>
                            <span>{order.vehicle.mileage.toLocaleString()} km</span>
                          </>
                        )}
                      </div>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-400">Información no disponible</p>
                  )}
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Taller</p>
                  <p className="text-sm">{order.workshop?.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Método de Llegada</p>
                  <p className="text-sm">{ARRIVAL_METHOD_LABELS[order.arrivalMethod]}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Tipo de Aprobación</p>
                  <p className="text-sm">{APPROVAL_TYPE_LABELS[order.approvalType]}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Fecha de Creación</p>
                  <p className="text-sm">
                    {format(new Date(order.createdAt), 'dd/MM/yyyy HH:mm', { locale: es })}
                  </p>
                </div>
                {order.totalEstimatedCost && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Costo Total Estimado</p>
                    <p className="text-sm font-semibold">
                      {formatCurrency(order.totalEstimatedCost)}
                    </p>
                  </div>
                )}
              </div>

              <Separator />

              <div>
                <p className="text-sm font-medium text-gray-500 mb-2">Descripción del Problema</p>
                <p className="text-sm bg-gray-50 p-3 rounded-md">
                  {order.customerDescription}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Diagnosis */}
          {order.diagnosisCompleted && order.diagnosisNotes && (
            <Card>
              <CardHeader>
                <CardTitle>Diagnóstico</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm bg-gray-50 p-3 rounded-md">
                  {order.diagnosisNotes}
                </p>
                {order.diagnosisDate && (
                  <p className="text-xs text-gray-500 mt-2">
                    Completado el {format(new Date(order.diagnosisDate), 'dd/MM/yyyy HH:mm', { locale: es })}
                  </p>
                )}
              </CardContent>
            </Card>
          )}

          {/* Waiting for Parts Actions */}
          {order.status === 'waiting-for-parts' && (
            <WaitingForPartsActions
              order={order}
              onManageParts={handleManageParts}
              onContinueWork={() => {
                // Logic to continue work with available services
                toast({
                  title: 'Continuando trabajo',
                  description: 'Iniciando servicios con refacciones disponibles...',
                });
                // Here you would call an API to continue work
                setTimeout(() => {
                  router.refresh();
                }, 1000);
              }}
              onRefreshOrder={() => {
                router.refresh();
              }}
            />
          )}

          {/* Services */}
          {order.services && order.services.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Servicios</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.services.map((service) => (
                    <div key={service._id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{service.serviceName}</h4>
                        <div className="flex items-center space-x-2">
                          <Badge
                            className={getStatusColor(service.status, 'service')}
                            variant="secondary"
                          >
                            {getStatusLabel(service.status, 'service')}
                          </Badge>
                          {(order.status === 'in-progress' || order.status === 'waiting-for-parts') && (
                            <>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleUpdateServiceProgress(service._id)}
                              >
                                Actualizar
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleManageParts(service._id)}
                              >
                                <Package className="h-3 w-3 mr-1" />
                                Refacciones
                              </Button>
                            </>
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{service.description}</p>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                        <div>
                          <span className="font-medium">Costo: </span>
                          {formatCurrency(service.estimatedCost)}
                        </div>
                        <div>
                          <span className="font-medium">Duración: </span>
                          {formatDuration(service.estimatedDuration)}
                        </div>
                        <div>
                          <span className="font-medium">Refacciones: </span>
                          {service.parts.length}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Estado de la Orden</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className={`flex items-center space-x-2 ${
                  ['pending', 'diagnosed', 'quoted', 'approved', 'in-progress', 'waiting-for-parts', 'completed'].includes(order.status)
                    ? 'text-green-600' : 'text-gray-400'
                }`}>
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Orden Creada</span>
                </div>
                <div className={`flex items-center space-x-2 ${
                  ['diagnosed', 'quoted', 'approved', 'in-progress', 'waiting-for-parts', 'completed'].includes(order.status)
                    ? 'text-green-600' : 'text-gray-400'
                }`}>
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Diagnóstico Completado</span>
                </div>

                {/* Evidencias del Diagnóstico */}
                {order.diagnosisCompleted && ((order.diagnosisEvidence?.photos && order.diagnosisEvidence.photos.length > 0) ||
                  (order.diagnosisEvidence?.videos && order.diagnosisEvidence.videos.length > 0)) && (
                  <div className="ml-6 mt-2 space-y-2">
                    <div className="flex items-center gap-2">
                      <FileText className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-600">
                        Evidencias ({(order.diagnosisEvidence?.photos?.length || 0) + (order.diagnosisEvidence?.videos?.length || 0)})
                      </span>
                    </div>
                    <div className="flex gap-1 flex-wrap">
                      {/* Mini thumbnails de fotos */}
                      {order.diagnosisEvidence?.photos?.slice(0, 4).map((photoUrl, index) => (
                        <div
                          key={`mini-photo-${index}`}
                          className="w-8 h-8 rounded border border-gray-200 overflow-hidden cursor-pointer hover:shadow-md transition-shadow group"
                          onClick={() => setSelectedImage({
                            url: photoUrl,
                            title: `Evidencia de Diagnóstico - Foto ${index + 1}`
                          })}
                        >
                          <img
                            src={photoUrl}
                            alt={`Mini evidencia ${index + 1}`}
                            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-200"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9IiNGM0Y0RjYiLz48cGF0aCBkPSJNMTIgMTBIMjBWMjJIMTJWMTBaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMC41Ii8+PHBhdGggZD0iTTE0IDE0SDE4VjE4SDE0VjE0WiIgZmlsbD0iIzlDQTNBRiIvPjwvc3ZnPg==';
                            }}
                          />
                        </div>
                      ))}

                      {/* Mini thumbnails de videos */}
                      {order.diagnosisEvidence?.videos?.slice(0, 2).map((videoUrl, index) => (
                        <div
                          key={`mini-video-${index}`}
                          className="w-8 h-8 rounded border border-gray-200 overflow-hidden cursor-pointer hover:shadow-md transition-shadow bg-purple-50 flex items-center justify-center group"
                          onClick={() => window.open(videoUrl, '_blank')}
                        >
                          <svg className="w-3 h-3 text-purple-600 group-hover:text-purple-700 transition-colors" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8 5v14l11-7z"/>
                          </svg>
                        </div>
                      ))}

                      {/* Indicador de más elementos */}
                      {((order.diagnosisEvidence?.photos?.length || 0) + (order.diagnosisEvidence?.videos?.length || 0)) > 6 && (
                        <div className="w-8 h-8 rounded border border-gray-200 bg-gray-100 flex items-center justify-center">
                          <span className="text-xs text-gray-500 font-medium">
                            +{((order.diagnosisEvidence?.photos?.length || 0) + (order.diagnosisEvidence?.videos?.length || 0)) - 6}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
                <div className={`flex items-center space-x-2 ${
                  ['quoted', 'approved', 'in-progress', 'waiting-for-parts', 'completed'].includes(order.status)
                    ? 'text-green-600' : 'text-gray-400'
                }`}>
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Cotización Creada</span>
                </div>
                <div className={`flex items-center space-x-2 ${
                  ['approved', 'in-progress', 'waiting-for-parts', 'completed'].includes(order.status)
                    ? 'text-green-600' : 'text-gray-400'
                }`}>
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Servicios Aprobados</span>
                </div>

                {/* Trabajo Iniciado - puede ser in-progress o waiting-for-parts */}
                <div className={`flex items-center space-x-2 ${
                  ['in-progress', 'waiting-for-parts', 'completed'].includes(order.status)
                    ? 'text-green-600' : 'text-gray-400'
                }`}>
                  <Wrench className="h-4 w-4" />
                  <span className="text-sm">Trabajo Iniciado</span>
                </div>



                {/* Evidencias de Servicios/Trabajos */}
                {order.services && order.services.length > 0 && (() => {
                  const servicesWithEvidence = order.services.filter(service =>
                    service.evidence && service.evidence.length > 0
                  );
                  const totalServiceEvidence = servicesWithEvidence.reduce((sum, service) =>
                    sum + (service.evidence?.length || 0), 0
                  );



                  return servicesWithEvidence.length > 0 && (
                    <div className="ml-6 mt-2 space-y-3">
                      <div className="flex items-center gap-2">
                        <Wrench className="h-3 w-3 text-gray-400" />
                        <span className="text-xs text-gray-600">
                          Evidencias de Trabajo ({totalServiceEvidence})
                        </span>
                      </div>

                      {/* Mostrar evidencias por servicio */}
                      {servicesWithEvidence.map((service, serviceIndex) => (
                        <div key={service._id} className="space-y-1">
                          <div className="text-xs text-gray-500 font-medium">
                            {service.serviceName}
                          </div>
                          <div className="flex gap-1 flex-wrap">
                            {/* Evidencias del servicio */}
                            {service.evidence?.slice(0, 4).map((evidence, index) => (
                              <div key={`service-${serviceIndex}-evidence-${index}`}>
                                {evidence.type === 'photo' ? (
                                  <div
                                    className="w-8 h-8 rounded border border-gray-200 overflow-hidden cursor-pointer hover:shadow-md transition-shadow group"
                                    onClick={() => setSelectedImage({
                                      url: evidence.url,
                                      title: `${service.serviceName} - Evidencia ${index + 1}`
                                    })}
                                  >
                                    <img
                                      src={evidence.url}
                                      alt={`Evidencia de ${service.serviceName} ${index + 1}`}
                                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-200"
                                      onError={(e) => {
                                        const target = e.target as HTMLImageElement;
                                        target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIGZpbGw9IiNGM0Y0RjYiLz48cGF0aCBkPSJNMTIgMTBIMjBWMjJIMTJWMTBaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMC41Ii8+PHBhdGggZD0iTTE0IDE0SDE4VjE4SDE0VjE0WiIgZmlsbD0iIzlDQTNBRiIvPjwvc3ZnPg==';
                                      }}
                                    />
                                  </div>
                                ) : (
                                  <div
                                    className="w-8 h-8 rounded border border-gray-200 overflow-hidden cursor-pointer hover:shadow-md transition-shadow bg-purple-50 flex items-center justify-center group"
                                    onClick={() => window.open(evidence.url, '_blank')}
                                  >
                                    <svg className="w-3 h-3 text-purple-600 group-hover:text-purple-700 transition-colors" fill="currentColor" viewBox="0 0 24 24">
                                      <path d="M8 5v14l11-7z"/>
                                    </svg>
                                  </div>
                                )}
                              </div>
                            ))}

                            {/* Indicador de más evidencias en este servicio */}
                            {(service.evidence?.length || 0) > 4 && (
                              <div className="w-8 h-8 rounded border border-gray-200 bg-gray-100 flex items-center justify-center">
                                <span className="text-xs text-gray-500 font-medium">
                                  +{(service.evidence?.length || 0) - 4}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  );
                })()}



                {/* Estado específico: En Progreso o Esperando Refacciones */}
                {order.status === 'in-progress' && (
                  <div className="flex items-center space-x-2 text-blue-600 ml-4">
                    <Clock className="h-4 w-4" />
                    <span className="text-sm">Trabajo en Progreso</span>
                  </div>
                )}

                {order.status === 'waiting-for-parts' && (
                  <div className="flex items-center space-x-2 text-yellow-600 ml-4">
                    <Package className="h-4 w-4" />
                    <span className="text-sm">Esperando Refacciones</span>
                  </div>
                )}

                <div className={`flex items-center space-x-2 ${
                  order.status === 'completed' ? 'text-green-600' : 'text-gray-400'
                }`}>
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-sm">Orden Completada</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          {order.services && order.services.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Resumen</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Total de Servicios:</span>
                  <span className="text-sm font-medium">{order.services.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Completados:</span>
                  <span className={`text-sm font-medium ${
                    allServicesCompleted ? 'text-green-600' : ''
                  }`}>
                    {order.services.filter(s => s.status === 'completed').length}
                    {allServicesCompleted && (
                      <span className="ml-1 text-green-600">✓ Todos</span>
                    )}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">En Progreso:</span>
                  <span className="text-sm font-medium">
                    {order.services.filter(s => s.status === 'in-progress').length}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Esperando Refacciones:</span>
                  <span className="text-sm font-medium">
                    {order.services.filter(s => s.status === 'waiting-for-parts').length}
                  </span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Parts Status */}
          {order.services && order.services.length > 0 && (
            <PartsStatusIndicator services={order.services} />
          )}
        </div>
      </div>

      {/* Modals */}
      <DiagnosisModal
        isOpen={isDiagnosisModalOpen}
        onClose={() => setIsDiagnosisModalOpen(false)}
        onSuccess={handleDiagnosisComplete}
        orderId={order._id}
      />

      <QuotationModal
        isOpen={isQuotationModalOpen}
        onClose={() => setIsQuotationModalOpen(false)}
        onSuccess={handleQuotationCreated}
        order={order}
        isClosingOrder={false} // Para crear cotización inicial
      />

      <ApprovalModal
        isOpen={isApprovalModalOpen}
        onClose={() => setIsApprovalModalOpen(false)}
        onSuccess={handleApprovalProcessed}
        quotation={order.quotation}
      />

      {selectedServiceId && (
        <ServiceProgressModal
          isOpen={isServiceProgressModalOpen}
          onClose={() => setIsServiceProgressModalOpen(false)}
          onSuccess={handleServiceProgressUpdated}
          serviceId={selectedServiceId}
          service={order.services?.find(s => s._id === selectedServiceId)}
        />
      )}

      <PartsManagementModal
        isOpen={isPartsManagementModalOpen && !!selectedServiceId}
        onClose={() => {
          setIsPartsManagementModalOpen(false);
          setSelectedServiceId(null);
          setSelectedServiceName('');
          setSelectedServiceType('');
        }}
        onSuccess={handlePartsManagementUpdated}
        serviceId={selectedServiceId || ''}
        serviceName={selectedServiceName}
        serviceType={selectedServiceType}
        existingParts={selectedServiceId ?
          convertServicePartsToModalParts(order.services?.find(s => s._id === selectedServiceId)?.parts || []) :
          []
        }
      />



      <VehicleHistoryModal
        isOpen={isVehicleHistoryModalOpen}
        onClose={handleVehicleHistoryClose}
        vehicleId={order.vehicle?._id || order.stockId || ''}
      />

      <OrderDetailsModal
        isOpen={isOrderDetailsModalOpen}
        onClose={() => setIsOrderDetailsModalOpen(false)}
        order={order}
      />

      <StartWorkConfirmationModal
        isOpen={isStartWorkConfirmationModalOpen}
        onClose={() => setIsStartWorkConfirmationModalOpen(false)}
        onSuccess={handleStartWorkConfirmed}
        order={order}
      />

      {/* Close Order Modal - Uses QuotationModal with isClosingOrder=true */}
      <QuotationModal
        isOpen={isCloseOrderModalOpen}
        onClose={() => setIsCloseOrderModalOpen(false)}
        onSuccess={handleOrderClosed}
        order={order}
        isClosingOrder={true} // Para cerrar la orden con evidencia final
      />

      {/* Modal para ver imágenes ampliadas */}
      {selectedImage && (
        <ImageModal
          isOpen={selectedImage !== null}
          onClose={() => setSelectedImage(null)}
          imageUrl={selectedImage.url}
          title={selectedImage.title}
        />
      )}
    </div>
  );
}
