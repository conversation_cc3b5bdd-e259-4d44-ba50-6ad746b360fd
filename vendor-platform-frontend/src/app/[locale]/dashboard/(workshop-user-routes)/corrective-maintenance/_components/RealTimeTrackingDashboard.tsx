'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Clock,
  Play,
  Pause,
  CheckCircle,
  AlertTriangle,
  Timer,
  Activity,
  RefreshCw,
  Eye
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';

interface ServicePhase {
  name: string;
  completed: boolean;
  startTime?: string;
  endTime?: string;
  duration?: number;
}

interface ServiceProgress {
  serviceId: string;
  serviceName: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  currentPhase?: string;
  progress: number; // 0-100
  timeSpent: number; // minutes
  estimatedTimeRemaining: number; // minutes
  isPaused: boolean;
  pauseReason?: string;
  lastActivity: string;
  phases: ServicePhase[];
}

interface RealTimeTrackingDashboardProps {
  orderId: string;
  refreshInterval?: number; // milliseconds
}

export default function RealTimeTrackingDashboard({
  orderId,
  refreshInterval = 30000
}: RealTimeTrackingDashboardProps) {
  const [services, setServices] = useState<ServiceProgress[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const { toast } = useToast();

  useEffect(() => {
    fetchOrderProgress();

    // Set up auto-refresh
    const interval = setInterval(fetchOrderProgress, refreshInterval);

    return () => clearInterval(interval);
  }, [orderId, refreshInterval]);

  const fetchOrderProgress = async () => {
    try {
      const response = await fetch(`/api/corrective-maintenance/orders/${orderId}/progress`);

      if (!response.ok) {
        throw new Error('Error fetching order progress');
      }

      const data = await response.json();
      setServices(data.data);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Error fetching order progress:', error);
      toast({
        title: 'Error',
        description: 'No se pudo actualizar el progreso de los servicios',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string, isPaused: boolean) => {
    if (isPaused) {
      return <Pause className="h-4 w-4 text-yellow-500" />;
    }

    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'in-progress':
        return <Play className="h-4 w-4 text-blue-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-gray-500" />;
      case 'cancelled':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const getPhaseProgress = (phases: ServicePhase[]): number => {
    const completedPhases = phases.filter(phase => phase.completed).length;
    return Math.round((completedPhases / phases.length) * 100);
  };

  const getCurrentPhaseDisplay = (currentPhase?: string): string => {
    const phaseNames: { [key: string]: string } = {
      'preparation': 'Preparación',
      'diagnosis': 'Diagnóstico',
      'work-in-progress': 'Trabajo en Progreso',
      'quality-check': 'Control de Calidad',
      'completed': 'Completado',
    };

    return phaseNames[currentPhase || ''] || currentPhase || 'Sin definir';
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          <span>Cargando progreso de servicios...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Seguimiento en Tiempo Real</h2>
          <p className="text-gray-600">
            Última actualización: {formatDistanceToNow(lastUpdated, {
              addSuffix: true,
              locale: es
            })}
          </p>
        </div>
        <Button onClick={fetchOrderProgress} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Actualizar
        </Button>
      </div>

      {/* Services Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Servicios Activos</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {services.filter(s => s.status === 'in-progress').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Servicios Pausados</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {services.filter(s => s.isPaused).length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Servicios Completados</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {services.filter(s => s.status === 'completed').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Services List */}
      <div className="space-y-4">
        {services.map((service) => (
          <Card key={service.serviceId} className="overflow-hidden">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getStatusIcon(service.status, service.isPaused)}
                  <div>
                    <CardTitle className="text-lg">{service.serviceName}</CardTitle>
                    <p className="text-sm text-gray-600">
                      Fase actual: {getCurrentPhaseDisplay(service.currentPhase)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(service.status)}>
                    {service.isPaused ? 'Pausado' : service.status}
                  </Badge>
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-1" />
                    Ver
                  </Button>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progreso General</span>
                  <span>{service.progress}%</span>
                </div>
                <Progress value={service.progress} className="h-2" />
              </div>

              {/* Time Information */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <Timer className="h-4 w-4 text-blue-500" />
                  <span>Tiempo transcurrido: {formatTime(service.timeSpent)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-green-500" />
                  <span>Tiempo restante: {formatTime(service.estimatedTimeRemaining)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Activity className="h-4 w-4 text-gray-500" />
                  <span>
                    Última actividad: {formatDistanceToNow(new Date(service.lastActivity), {
                      addSuffix: true,
                      locale: es
                    })}
                  </span>
                </div>
              </div>

              {/* Pause Alert */}
              {service.isPaused && service.pauseReason && (
                <Alert className="border-yellow-200 bg-yellow-50">
                  <Pause className="h-4 w-4 text-yellow-600" />
                  <AlertDescription className="text-yellow-800">
                    Servicio pausado: {service.pauseReason}
                  </AlertDescription>
                </Alert>
              )}

              {/* Phases Progress */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Fases del Servicio</h4>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
                  {service.phases.map((phase, index) => (
                    <div
                      key={index}
                      className={`p-2 rounded text-xs text-center ${
                        phase.completed
                          ? 'bg-green-100 text-green-800'
                          : service.currentPhase === phase.name
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-600'
                      }`}
                    >
                      <div className="font-medium">
                        {getCurrentPhaseDisplay(phase.name)}
                      </div>
                      {phase.completed && (
                        <CheckCircle className="h-3 w-3 mx-auto mt-1" />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {services.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-600">No hay servicios en progreso para esta orden.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
