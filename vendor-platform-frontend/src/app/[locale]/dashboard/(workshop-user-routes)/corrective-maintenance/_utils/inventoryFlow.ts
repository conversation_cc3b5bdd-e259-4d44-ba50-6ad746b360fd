import { ServicePart } from '@/constants/correctiveMaintenanceService';

export interface InventoryFlowDecision {
  shouldEnterInventoryFlow: boolean;
  reason: string;
  estimatedDelay?: number; // in hours
  nextAction: 'proceed' | 'wait_for_parts' | 'order_parts';
}

export interface PartAvailabilityCheck {
  partId: string;
  partName: string;
  availability: 'available' | 'pending' | 'unavailable';
  quantity: number;
  inStock?: number;
  estimatedArrival?: string;
}

/**
 * Determines if a service should enter the inventory flow based on parts availability
 */
export function shouldEnterInventoryFlow(parts: ServicePart[]): InventoryFlowDecision {
  if (!parts || parts.length === 0) {
    return {
      shouldEnterInventoryFlow: false,
      reason: 'No hay refacciones requeridas para este servicio',
      nextAction: 'proceed'
    };
  }

  const unavailableParts = parts.filter(part => part.availability === 'unavailable');
  const pendingParts = parts.filter(part => part.availability === 'pending');

  // If all parts are available, proceed without inventory flow
  if (unavailableParts.length === 0 && pendingParts.length === 0) {
    return {
      shouldEnterInventoryFlow: false,
      reason: 'Todas las refacciones están disponibles',
      nextAction: 'proceed'
    };
  }

  // If there are unavailable parts, enter inventory flow
  if (unavailableParts.length > 0) {
    const estimatedDelay = calculateEstimatedDelay(unavailableParts);
    return {
      shouldEnterInventoryFlow: true,
      reason: `${unavailableParts.length} refacción(es) no disponible(s): ${unavailableParts.map(p => p.name).join(', ')}`,
      estimatedDelay,
      nextAction: 'order_parts'
    };
  }

  // If there are pending parts, wait for verification
  if (pendingParts.length > 0) {
    return {
      shouldEnterInventoryFlow: true,
      reason: `${pendingParts.length} refacción(es) pendiente(s) de verificar: ${pendingParts.map(p => p.name).join(', ')}`,
      estimatedDelay: 2, // 2 hours for verification
      nextAction: 'wait_for_parts'
    };
  }

  return {
    shouldEnterInventoryFlow: false,
    reason: 'Estado de refacciones no determinado',
    nextAction: 'proceed'
  };
}

/**
 * Calculates estimated delay based on unavailable parts
 */
function calculateEstimatedDelay(unavailableParts: ServicePart[]): number {
  let maxDelay = 0;

  unavailableParts.forEach(part => {
    let partDelay = 24; // Default 24 hours

    if (part.eta) {
      const etaDate = new Date(part.eta);
      const now = new Date();
      const diffInHours = Math.max(0, (etaDate.getTime() - now.getTime()) / (1000 * 60 * 60));
      partDelay = diffInHours;
    } else {
      // Estimate based on part type or supplier
      if (part.supplier) {
        partDelay = 48; // 48 hours if supplier is known
      } else {
        partDelay = 72; // 72 hours if no supplier specified
      }
    }

    maxDelay = Math.max(maxDelay, partDelay);
  });

  return maxDelay;
}

/**
 * Determines the next steps for inventory management
 */
export function getInventoryNextSteps(parts: ServicePart[]): {
  availableParts: ServicePart[];
  partsToOrder: ServicePart[];
  partsToVerify: ServicePart[];
  canStartService: boolean;
  estimatedStartTime?: Date;
} {
  const availableParts = parts.filter(part => part.availability === 'available');
  const partsToOrder = parts.filter(part => part.availability === 'unavailable');
  const partsToVerify = parts.filter(part => part.availability === 'pending');

  const canStartService = partsToOrder.length === 0 && partsToVerify.length === 0;

  let estimatedStartTime: Date | undefined;
  if (!canStartService) {
    const decision = shouldEnterInventoryFlow(parts);
    if (decision.estimatedDelay) {
      estimatedStartTime = new Date();
      estimatedStartTime.setHours(estimatedStartTime.getHours() + decision.estimatedDelay);
    }
  }

  return {
    availableParts,
    partsToOrder,
    partsToVerify,
    canStartService,
    estimatedStartTime
  };
}

/**
 * Creates inventory orders for unavailable parts
 */
export function createInventoryOrders(parts: ServicePart[], serviceId: string) {
  const partsToOrder = parts.filter(part => part.availability === 'unavailable');
  
  return partsToOrder.map(part => ({
    serviceId,
    partName: part.name,
    partNumber: part.partNumber,
    quantity: part.quantity,
    totalCost: part.totalCost,
    supplier: part.supplier,
    estimatedArrival: part.eta,
    status: 'pending' as const,
    orderDate: new Date().toISOString(),
    priority: 'normal' as 'low' | 'normal' | 'high' | 'urgent'
  }));
}

/**
 * Updates service status based on parts availability
 */
export function updateServiceStatusBasedOnParts(parts: ServicePart[], currentStatus: string): string {
  const decision = shouldEnterInventoryFlow(parts);
  
  if (!decision.shouldEnterInventoryFlow) {
    // All parts available, can proceed
    return currentStatus === 'waiting-for-parts' ? 'not-started' : currentStatus;
  }

  if (decision.nextAction === 'order_parts' || decision.nextAction === 'wait_for_parts') {
    return 'waiting-for-parts';
  }

  return currentStatus;
}

/**
 * Generates inventory flow notifications
 */
export function generateInventoryNotifications(parts: ServicePart[], serviceName: string) {
  const decision = shouldEnterInventoryFlow(parts);
  const notifications = [];

  if (decision.shouldEnterInventoryFlow) {
    notifications.push({
      type: 'inventory_required',
      title: 'Refacciones requeridas',
      message: `El servicio "${serviceName}" requiere gestión de inventarios: ${decision.reason}`,
      priority: decision.nextAction === 'order_parts' ? 'high' : 'medium',
      estimatedDelay: decision.estimatedDelay
    });

    const partsToOrder = parts.filter(part => part.availability === 'unavailable');
    if (partsToOrder.length > 0) {
      notifications.push({
        type: 'parts_order_required',
        title: 'Pedido de refacciones necesario',
        message: `Se requiere ordenar ${partsToOrder.length} refacción(es) para el servicio "${serviceName}"`,
        priority: 'high',
        parts: partsToOrder.map(p => ({ name: p.name, quantity: p.quantity }))
      });
    }
  }

  return notifications;
}
