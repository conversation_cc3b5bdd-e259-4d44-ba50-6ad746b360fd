'use server';

import { URL_API } from '@/constants';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { ApiResponse, CorrectiveMaintenanceOrder } from '../types';
import { updateServiceProgress } from './updateServiceProgress';

export async function startWork(
  orderId: string
): Promise<ApiResponse<CorrectiveMaintenanceOrder>> {
  const user = await getCurrentUser();

  if (!user) {
    return {
      success: false,
      data: null as any,
      message: 'Usuario no autenticado',
    };
  }

  try {
    console.log('🔗 Starting work for order:', orderId);

    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/orders/${orderId}/start`,
      {},
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log('✅ Work started successfully:', response.data);

    // Transform API response to match our expected format
    const apiResponse = response.data;
    const orderData = apiResponse.data || apiResponse;

    // WORKAROUND: Fix service statuses that were incorrectly set to 'in-progress' by backend
    // Services should start as 'not-started' and be manually started by technicians
    if (orderData && orderData.services && Array.isArray(orderData.services)) {
      console.log('🔧 Correcting service statuses to not-started...');
      
      // Get services that were incorrectly set to 'in-progress'
      const servicesToCorrect = orderData.services.filter((service: any) => 
        service.status === 'in-progress'
      );

      if (servicesToCorrect.length > 0) {
        console.log(`📝 Found ${servicesToCorrect.length} services to correct from 'in-progress' to 'not-started'`);
        
        // Update each service to 'not-started' status
        const correctionPromises = servicesToCorrect.map(async (service: any) => {
          try {
            const correctionResult = await updateServiceProgress(service._id, {
              status: 'not-started',
              notes: 'Status corrected: Services should start as not-started when work begins'
            });
            
            if (correctionResult.success) {
              console.log(`✅ Corrected service ${service._id} to not-started`);
              // Update the service status in our local data
              service.status = 'not-started';
            } else {
              console.error(`❌ Failed to correct service ${service._id}:`, correctionResult.message);
            }
            
            return correctionResult;
          } catch (error) {
            console.error(`❌ Error correcting service ${service._id}:`, error);
            return { success: false, message: 'Error correcting service status' };
          }
        });

        // Wait for all corrections to complete
        await Promise.all(correctionPromises);
        console.log('🎯 Service status corrections completed');
      } else {
        console.log('✅ No services need status correction');
      }
    }

    return {
      success: true,
      data: orderData,
      message: apiResponse.message || 'Trabajo iniciado exitosamente',
    };
  } catch (error: any) {
    console.error('❌ Error starting work:', error.response?.data || error);

    let errorMessage = 'Error al iniciar el trabajo';
    
    if (error.response?.status === 404) {
      errorMessage = 'La orden no fue encontrada';
    } else if (error.response?.status === 401) {
      errorMessage = 'No autorizado para iniciar el trabajo';
    } else if (error.response?.status === 400) {
      errorMessage = error.response?.data?.message || 'La orden no está en un estado válido para iniciar el trabajo';
    } else if (error.response?.status === 500) {
      errorMessage = 'Error interno del servidor al iniciar el trabajo';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = 'No se puede conectar con el servidor de la API';
    }

    return {
      success: false,
      data: null as any,
      message: errorMessage,
    };
  }
}
