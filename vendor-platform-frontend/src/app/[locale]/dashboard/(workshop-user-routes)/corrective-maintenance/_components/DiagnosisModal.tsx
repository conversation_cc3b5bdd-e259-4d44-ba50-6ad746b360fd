'use client';

import { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Plus, Trash2, Upload, X, Image } from 'lucide-react';
import { SERVICE_TYPES } from '../types';
import { completeDiagnosis } from '../_actions/completeDiagnosis';
import { URL_API } from '@/constants';
import { useCurrentUser } from '@/Providers/CurrentUserProvider';
import axios from 'axios';

const diagnosisSchema = z.object({
  diagnosisNotes: z.string().min(20, 'Las notas del diagnóstico deben tener al menos 20 caracteres'),
  services: z.array(z.object({
    serviceType: z.string().min(1, 'Selecciona un tipo de servicio'),
    serviceName: z.string().min(3, 'El nombre del servicio debe tener al menos 3 caracteres'),
    description: z.string().min(10, 'La descripción debe tener al menos 10 caracteres'),
    estimatedCost: z.number().min(1, 'El costo debe ser mayor a 0'),
    estimatedDuration: z.number().min(0.5, 'La duración debe ser al menos 0.5 horas'),
    evidence: z.array(z.instanceof(File)).optional(), // Make evidence optional in schema, we'll validate separately
    parts: z.array(z.object({
      name: z.string().min(1, 'Nombre de la refacción requerido'),
      quantity: z.number().min(1, 'La cantidad debe ser mayor a 0'),
      supplier: z.string().optional(),
      partNumber: z.string().optional(),
    })).optional(),
  })).min(1, 'Debe agregar al menos un servicio'),
});

type DiagnosisFormData = z.infer<typeof diagnosisSchema>;

interface DiagnosisModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  orderId: string;
}

export default function DiagnosisModal({ isOpen, onClose, onSuccess, orderId }: DiagnosisModalProps) {
  const { toast } = useToast();
  const { user } = useCurrentUser();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [serviceEvidence, setServiceEvidence] = useState<{ [serviceIndex: number]: File[] }>({});



  const form = useForm<DiagnosisFormData>({
    resolver: zodResolver(diagnosisSchema),
    defaultValues: {
      diagnosisNotes: '',
      services: [{
        serviceType: '',
        serviceName: '',
        description: '',
        estimatedCost: 0,
        estimatedDuration: 1,
        evidence: [],
        parts: [],
      }],
    },
  });

  const { fields: serviceFields, append: appendService, remove: removeService } = useFieldArray({
    control: form.control,
    name: 'services',
  });

  const handleServiceFileChange = (serviceIndex: number, files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files);
      const newFiles = [...(serviceEvidence[serviceIndex] || []), ...fileArray];

      setServiceEvidence(prev => ({
        ...prev,
        [serviceIndex]: newFiles
      }));

      form.setValue(`services.${serviceIndex}.evidence`, newFiles);
    }
  };

  const removeServiceFile = (serviceIndex: number, fileIndex: number) => {
    const currentFiles = serviceEvidence[serviceIndex] || [];
    const newFiles = [...currentFiles];
    newFiles.splice(fileIndex, 1);

    setServiceEvidence(prev => ({
      ...prev,
      [serviceIndex]: newFiles
    }));

    form.setValue(`services.${serviceIndex}.evidence`, newFiles);
  };

  const onSubmit = async (data: DiagnosisFormData) => {

    // Validate that each service has evidence
    const servicesWithoutEvidence = data.services.filter((service, index) => {
      const hasEvidence = serviceEvidence[index] && serviceEvidence[index].length > 0;
      return !hasEvidence;
    });

    if (servicesWithoutEvidence.length > 0) {
      toast({
        title: 'Error de validación',
        description: 'Todos los servicios deben tener al menos una evidencia (foto o video)',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Call the API directly from the client to handle File objects properly
      const response = await submitDiagnosisDirectly(data);

      if (response.success) {
        toast({
          title: 'Éxito',
          description: 'Diagnóstico completado exitosamente',
        });
        form.reset();
        setServiceEvidence({});
        onSuccess();
      } else {
        toast({
          title: 'Error',
          description: response.message || 'Error al completar el diagnóstico',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error completing diagnosis:', error);
      toast({
        title: 'Error',
        description: 'Error inesperado al completar el diagnóstico',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const submitDiagnosisDirectly = async (data: DiagnosisFormData) => {
    // Check if user is available
    if (!user || !user.accessToken) {
      throw new Error('Usuario no autenticado o token no disponible');
    }

    // Create FormData for multipart/form-data request
    const formData = new FormData();
    formData.append('diagnosisNotes', data.diagnosisNotes);

    // Prepare services data without evidence files for JSON
    const servicesForJson = data.services.map((service, index) => ({
      serviceType: service.serviceType,
      serviceName: service.serviceName,
      description: service.description,
      estimatedCost: service.estimatedCost,
      estimatedDuration: service.estimatedDuration,
      parts: (service.parts || []).map(part => ({
        name: part.name,
        quantity: part.quantity,
        supplier: part.supplier || '',
        partNumber: part.partNumber || '',
        availability: 'available' as const
      }))
    }));

    formData.append('services', JSON.stringify(servicesForJson));

    // Append evidence files for each service
    data.services.forEach((service, serviceIndex) => {
      const evidence = serviceEvidence[serviceIndex] || [];
      if (evidence.length > 0) {
        evidence.forEach((file) => {
          formData.append(`service_${serviceIndex}_evidence`, file);
        });
      }
    });



    const response = await axios.post(
      `${URL_API}/vendor-platform/corrective-maintenance/orders/${orderId}/diagnosis`,
      formData,
      {
        headers: {
          Authorization: `Bearer ${user.accessToken}`,
          'Content-Type': 'multipart/form-data',
        },
      }
    );



    // Transform API response to match our expected format
    const apiResponse = response.data;

    return {
      success: true,
      data: apiResponse.data || apiResponse,
      message: apiResponse.message || 'Diagnóstico completado exitosamente',
    };
  };

  const handleClose = () => {
    form.reset();
    setServiceEvidence({});
    onClose();
  };

  const handleSubmitClick = async () => {
    const formData = form.getValues();

    // Trigger form validation
    const isValid = await form.trigger();

    if (isValid) {
      await onSubmit(formData);
    } else {
      toast({
        title: 'Error de validación',
        description: 'Por favor, completa todos los campos requeridos',
        variant: 'destructive',
      });
    }
  };

  const addService = () => {
    appendService({
      serviceType: '',
      serviceName: '',
      description: '',
      estimatedCost: 0,
      estimatedDuration: 1,
      evidence: [],
      parts: [],
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Completar Diagnóstico</DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Diagnosis Notes */}
          <div className="space-y-2">
            <Label htmlFor="diagnosisNotes">Notas del Diagnóstico *</Label>
            <Textarea
              {...form.register('diagnosisNotes')}
              placeholder="Describe detalladamente los hallazgos del diagnóstico..."
              rows={4}
            />
            {form.formState.errors.diagnosisNotes && (
              <p className="text-sm text-red-500">{form.formState.errors.diagnosisNotes.message}</p>
            )}
          </div>



          {/* Services */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label>Servicios Requeridos *</Label>
              <Button type="button" variant="outline" onClick={addService}>
                <Plus className="h-4 w-4 mr-2" />
                Agregar Servicio
              </Button>
            </div>

            {serviceFields.map((field, index) => (
              <div key={field.id} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Servicio {index + 1}</h4>
                  {serviceFields.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        removeService(index);
                        // Clean up evidence for this service
                        setServiceEvidence(prev => {
                          const newEvidence = { ...prev };
                          delete newEvidence[index];
                          // Reindex remaining evidence
                          const reindexedEvidence: { [key: number]: File[] } = {};
                          Object.keys(newEvidence).forEach((key, newIndex) => {
                            const keyNum = parseInt(key);
                            if (keyNum > index) {
                              reindexedEvidence[keyNum - 1] = newEvidence[keyNum];
                            } else {
                              reindexedEvidence[keyNum] = newEvidence[keyNum];
                            }
                          });
                          return reindexedEvidence;
                        });
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Service Type */}
                  <div className="space-y-2">
                    <Label>Tipo de Servicio *</Label>
                    <Select
                      value={form.watch(`services.${index}.serviceType`) || undefined}
                      onValueChange={(value) => {
                        form.setValue(`services.${index}.serviceType`, value);
                        // Auto-fill service name based on type
                        const serviceType = SERVICE_TYPES.find(s => s.value === value);
                        if (serviceType) {
                          form.setValue(`services.${index}.serviceName`, serviceType.label);
                        }
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Selecciona un tipo" />
                      </SelectTrigger>
                      <SelectContent>
                        {SERVICE_TYPES.map((service) => (
                          <SelectItem key={service.value} value={service.value}>
                            {service.label} ({service.category})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {form.formState.errors.services?.[index]?.serviceType && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.services[index]?.serviceType?.message}
                      </p>
                    )}
                  </div>

                  {/* Service Name */}
                  <div className="space-y-2">
                    <Label>Nombre del Servicio *</Label>
                    <Input
                      {...form.register(`services.${index}.serviceName`)}
                      placeholder="Ej: Cambio de pastillas de freno delanteras"
                    />
                    {form.formState.errors.services?.[index]?.serviceName && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.services[index]?.serviceName?.message}
                      </p>
                    )}
                  </div>

                  {/* Estimated Cost */}
                  <div className="space-y-2">
                    <Label>Costo Estimado (MXN) *</Label>
                    <Input
                      type="number"
                      step="0.01"
                      {...form.register(`services.${index}.estimatedCost`, { valueAsNumber: true })}
                      placeholder="0.00"
                    />
                    {form.formState.errors.services?.[index]?.estimatedCost && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.services[index]?.estimatedCost?.message}
                      </p>
                    )}
                  </div>


                  {/* Estimated Duration */}
                  <div className="space-y-2 md:col-span-2">
                    <Label>Duración Estimada (horas) *</Label>
                    <Input
                      type="number"
                      step="0.5"
                      {...form.register(`services.${index}.estimatedDuration`, { valueAsNumber: true })}
                      placeholder="1.0"
                    />
                    {form.formState.errors.services?.[index]?.estimatedDuration && (
                      <p className="text-sm text-red-500">
                        {form.formState.errors.services[index]?.estimatedDuration?.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <Label>Descripción del Servicio *</Label>
                  <Textarea
                    {...form.register(`services.${index}.description`)}
                    placeholder="Describe detalladamente el trabajo a realizar..."
                    rows={3}
                  />
                  {form.formState.errors.services?.[index]?.description && (
                    <p className="text-sm text-red-500">
                      {form.formState.errors.services[index]?.description?.message}
                    </p>
                  )}
                </div>

                {/* Evidence Upload Section for this Service */}
                <div className="space-y-4">
                  <Label>Evidencia del Servicio (Fotos/Videos) *</Label>

                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                    <div className="text-center">
                      <Upload className="mx-auto h-8 w-8 text-gray-400" />
                      <div className="mt-2">
                        <label htmlFor={`service-evidence-upload-${index}`} className="cursor-pointer">
                          <span className="mt-1 block text-sm font-medium text-gray-900">
                            Subir evidencia para este servicio
                          </span>
                          <span className="mt-1 block text-xs text-gray-500">
                            PNG, JPG, MP4 hasta 10MB cada uno (Requerido)
                          </span>
                        </label>
                        <input
                          id={`service-evidence-upload-${index}`}
                          type="file"
                          multiple
                          accept="image/*,video/*"
                          onChange={(e) => handleServiceFileChange(index, e.target.files)}
                          className="hidden"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Display uploaded files for this service */}
                  {serviceEvidence[index] && serviceEvidence[index].length > 0 && (
                    <div className="space-y-2">
                      <Label>Archivos seleccionados ({serviceEvidence[index].length})</Label>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {serviceEvidence[index].map((file, fileIndex) => (
                          <div key={fileIndex} className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-2">
                              <Image className="h-4 w-4 text-gray-500" />
                              <span className="text-sm text-gray-700 truncate">
                                {file.name}
                              </span>
                              <span className="text-xs text-gray-500">
                                ({(file.size / 1024 / 1024).toFixed(1)} MB)
                              </span>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeServiceFile(index, fileIndex)}
                              className="h-6 w-6 p-0"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Evidence validation error */}
                  {form.formState.errors.services?.[index]?.evidence && (
                    <p className="text-sm text-red-500">
                      {form.formState.errors.services[index]?.evidence?.message}
                    </p>
                  )}
                </div>
              </div>
            ))}

            {form.formState.errors.services && (
              <p className="text-sm text-red-500">
                {form.formState.errors.services.message}
              </p>
            )}
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancelar
            </Button>
            <Button
              type="button"
              disabled={isSubmitting}
              onClick={async (e) => {
                e.preventDefault();
                await handleSubmitClick();
              }}
            >
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Completar Diagnóstico
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
