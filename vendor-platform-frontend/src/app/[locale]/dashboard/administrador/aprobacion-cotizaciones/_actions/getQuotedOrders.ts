'use server';

import { URL_API } from '@/constants';
import { cache } from 'react';
import axios from 'axios';
import getCurrentUser from '@/actions/getCurrentUser';
import { CorrectiveMaintenanceOrder, ApiResponse } from '@/app/[locale]/dashboard/(workshop-user-routes)/corrective-maintenance/types';

interface GetQuotedOrdersParams {
  page?: number;
  limit?: number;
}

export const getQuotedOrders = cache(async (params: GetQuotedOrdersParams = {}) => {
  const user = await getCurrentUser();

  if (!user) {
    return {
      success: false,
      data: [],
      message: 'Usuario no autenticado',
      pagination: {
        page: params.page || 1,
        limit: params.limit || 50,
        total: 0,
        totalPages: 0,
      },
    } as ApiResponse<CorrectiveMaintenanceOrder[]>;
  }

  // Check if user is superAdmin
  const isSuperAdmin = user.userType === 'superAdmin' || 
                       user.role === 'administrador' || 
                       user.role === 'superadmin';

  if (!isSuperAdmin) {
    return {
      success: false,
      data: [],
      message: 'No tienes permisos para acceder a esta información',
      pagination: {
        page: params.page || 1,
        limit: params.limit || 50,
        total: 0,
        totalPages: 0,
      },
    } as ApiResponse<CorrectiveMaintenanceOrder[]>;
  }

  try {
    // Use the quotations endpoint to get quotations that need approval
    const quotationsUrl = new URL(`${URL_API}/vendor-platform/corrective-maintenance/quotations`);
    
    // Add query parameters
    if (params.page) quotationsUrl.searchParams.append('page', params.page.toString());
    if (params.limit) quotationsUrl.searchParams.append('limit', params.limit.toString());

    const quotationsResponse = await axios.get(quotationsUrl.toString(), {
      headers: {
        Authorization: `Bearer ${user.accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    const quotationsApiResponse = quotationsResponse.data;
    const quotations = quotationsApiResponse.data || [];
    // The quotations API returns orders populated in the orderId field
    // Extract the actual orders and quotations from the response
    let ordersWithQuotations: CorrectiveMaintenanceOrder[] = [];
    
    if (quotations.length > 0) {
      const sampleQuotation = quotations[0];
      const orderIdType = typeof sampleQuotation?.orderId;
      
      if (orderIdType === 'object' && sampleQuotation?.orderId?._id) {
        // orderId contains full order objects - filter quotations that need approval
        const quotationsNeedingApproval = quotations.filter((quotation: any) => {
          return quotation.orderId && quotation.orderId._id && 
            ['pending-approval', 'pending-customer', 'escalated-to-fleet', 'draft'].includes(quotation.status);
        });
        
        // Map quotations to orders with quotation data attached
        ordersWithQuotations = quotationsNeedingApproval.map((quotation: any) => ({
          ...quotation.orderId,
          quotation: {
            _id: quotation._id,
            orderId: quotation.orderId._id,
            quotationNumber: quotation.quotationNumber,
            status: quotation.status,
            services: quotation.services,
            totalCost: quotation.totalCost,
            validUntil: quotation.validUntil,
            createdAt: quotation.createdAt,
            updatedAt: quotation.updatedAt,
            approvalType: quotation.approvalType,
            customerApprovalDeadline: quotation.customerApprovalDeadline,
            escalatedToFleetAt: quotation.escalatedToFleetAt,
            customerNotificationSent: quotation.customerNotificationSent,
            customerResponseTimeout: quotation.customerResponseTimeout
          }
        }));
      } else {
        // orderId contains string IDs - get corresponding orders
        const orderIds = quotations
          .map((q: any) => q.orderId)
          .filter(Boolean)
          .filter((id: string) => ['pending-approval', 'pending-customer', 'escalated-to-fleet', 'draft'].includes(
            quotations.find((q: any) => q.orderId === id)?.status
          ));
        
        if (orderIds.length > 0) {
          const ordersUrl = new URL(`${URL_API}/vendor-platform/corrective-maintenance/orders`);
          ordersUrl.searchParams.append('page', '1');
          ordersUrl.searchParams.append('limit', '100');

          const ordersResponse = await axios.get(ordersUrl.toString(), {
            headers: {
              Authorization: `Bearer ${user.accessToken}`,
              'Content-Type': 'application/json',
            },
          });

          const ordersApiResponse = ordersResponse.data;
          const allOrders = ordersApiResponse.data || [];
          
          // Filter orders that have matching quotations needing approval
          const ordersWithMatchingQuotations = allOrders.filter((order: CorrectiveMaintenanceOrder) => 
            orderIds.includes(order._id)
          );
          
          // Attach the quotation data to the orders
          ordersWithQuotations = ordersWithMatchingQuotations.map((order: CorrectiveMaintenanceOrder) => {
            const matchingQuotation = quotations.find((q: any) => q.orderId === order._id);
            return {
              ...order,
              quotation: matchingQuotation
            };
          });
        }
      }
    }

    const orders = ordersWithQuotations;

    return {
      success: true,
      data: orders,
      message: quotationsApiResponse.message || 'Cotizaciones obtenidas exitosamente',
      pagination: {
        page: quotationsApiResponse.pagination?.page || params.page || 1,
        limit: quotationsApiResponse.pagination?.limit || params.limit || 50,
        total: orders.length,
        totalPages: Math.ceil(orders.length / (params.limit || 50)),
      },
    } as ApiResponse<CorrectiveMaintenanceOrder[]>;
  } catch (error: any) {
    console.error('Error fetching quoted orders:', error);

    // Determine error message based on status code
    let errorMessage = 'Error al obtener las cotizaciones pendientes';

    if (error.response?.status === 404) {
      errorMessage = 'El endpoint de cotizaciones no está disponible';
    } else if (error.response?.status === 401) {
      errorMessage = 'No autorizado para acceder a las cotizaciones';
    } else if (error.response?.status === 500) {
      errorMessage = 'Error interno del servidor al obtener las cotizaciones';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = 'No se puede conectar con el servidor de la API';
    }

    return {
      success: false,
      data: [],
      message: errorMessage,
      pagination: {
        page: params.page || 1,
        limit: params.limit || 50,
        total: 0,
        totalPages: 0,
      },
    } as ApiResponse<CorrectiveMaintenanceOrder[]>;
  }
});