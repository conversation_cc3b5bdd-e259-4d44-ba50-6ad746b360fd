'use client';

import { useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Plus, Trash2, Package, Clock, CheckCircle, AlertTriangle } from 'lucide-react';
import { formatCurrency } from '../types';

const partSchema = z.object({
  partName: z.string().min(1, 'Nombre de la refacción requerido'),
  partNumber: z.string().optional(),
  quantity: z.number().min(1, 'Cantidad debe ser mayor a 0'),
  unitCost: z.number().min(0, 'Costo debe ser mayor o igual a 0'),
  supplier: z.string().optional(),
  estimatedArrival: z.string().optional(),
  status: z.enum(['pending', 'ordered', 'in-transit', 'arrived', 'installed']),
  notes: z.string().optional(),
});

const partsManagementSchema = z.object({
  serviceId: z.string(),
  parts: z.array(partSchema).min(1, 'Debe incluir al menos una refacción'),
});

// Schema for brake services that allows empty parts array
const brakeServicePartsManagementSchema = z.object({
  serviceId: z.string(),
  parts: z.array(partSchema), // No minimum requirement for brake services
});

type PartsManagementFormData = z.infer<typeof partsManagementSchema>;

interface Part {
  _id?: string;
  partName: string;
  partNumber?: string;
  quantity: number;
  unitCost: number;
  supplier?: string;
  estimatedArrival?: string;
  status: 'pending' | 'ordered' | 'in-transit' | 'arrived' | 'installed';
  notes?: string;
}

interface PartsManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  serviceId: string;
  serviceName: string;
  serviceType?: string;
  existingParts?: Part[];
}

export default function PartsManagementModal({
  isOpen,
  onClose,
  onSuccess,
  serviceId,
  serviceName,
  serviceType,
  existingParts = []
}: PartsManagementModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check if this is a brake service - disable add part functionality for brake services
  const isBrakeService = serviceType === 'brakes' || serviceName.toLowerCase().includes('freno');

  const form = useForm<PartsManagementFormData>({
    resolver: zodResolver(isBrakeService ? brakeServicePartsManagementSchema : partsManagementSchema),
    defaultValues: {
      serviceId,
      parts: existingParts.length > 0 ? existingParts :
        // For brake services, don't create empty part if no existing parts
        (isBrakeService ? [] : [{
          partName: '',
          partNumber: '',
          quantity: 1,
          unitCost: 0,
          supplier: '',
          estimatedArrival: '',
          status: 'pending',
          notes: '',
        }]),
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'parts',
  });

  const watchedParts = form.watch('parts');
  const totalCost = watchedParts.reduce((sum, part) => sum + (part.quantity * part.unitCost), 0);

  const addPart = () => {
    append({
      partName: '',
      partNumber: '',
      quantity: 1,
      unitCost: 0,
      supplier: '',
      estimatedArrival: '',
      status: 'pending',
      notes: '',
    });
  };

  const removePart = (index: number) => {
    // For brake services, allow removing all parts
    // For regular services, keep at least one part
    if (isBrakeService || fields.length > 1) {
      remove(index);
    }
  };

  const onSubmit = async (data: PartsManagementFormData) => {
    setIsSubmitting(true);
    try {
      // Here you would call your API to update parts
      console.log('Updating parts:', data);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: 'Éxito',
        description: 'Refacciones actualizadas exitosamente',
      });

      form.reset();
      onSuccess();
    } catch (error) {
      console.error('Error updating parts:', error);
      toast({
        title: 'Error',
        description: 'Error inesperado al actualizar las refacciones',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    form.reset();
    onClose();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'ordered': return <Package className="h-4 w-4" />;
      case 'in-transit': return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'arrived': return <CheckCircle className="h-4 w-4" />;
      case 'installed': return <CheckCircle className="h-4 w-4" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-gray-100 text-gray-800';
      case 'ordered': return 'bg-blue-100 text-blue-800';
      case 'in-transit': return 'bg-yellow-100 text-yellow-800';
      case 'arrived': return 'bg-green-100 text-green-800';
      case 'installed': return 'bg-green-100 text-green-800';
      default: return 'bg-red-100 text-red-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return 'Pendiente';
      case 'ordered': return 'Ordenada';
      case 'in-transit': return 'En Tránsito';
      case 'arrived': return 'Llegó';
      case 'installed': return 'Instalada';
      default: return 'Desconocido';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Gestión de Refacciones - {serviceName}</DialogTitle>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Parts List */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Refacciones Necesarias</h3>

            </div>

            {/* Information message for brake services */}
            {isBrakeService && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-sm font-medium text-blue-900">
                    Servicio de Frenos Detectado
                  </p>
                  <p className="text-sm text-blue-700 mt-1">
                    Para servicios de frenos, solo se pueden gestionar las refacciones existentes.
                    No es posible agregar nuevas refacciones por política de seguridad.
                  </p>
                </div>
              </div>
            )}

            {/* Show message when brake service has no parts */}
            {isBrakeService && fields.length === 0 && (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                <p className="text-sm font-medium text-gray-900 mb-2">
                  No hay refacciones registradas
                </p>
                <p className="text-sm text-gray-600">
                  Este servicio de frenos aún no tiene refacciones asociadas.
                  Las refacciones serán agregadas por el sistema según sea necesario.
                </p>
              </div>
            )}

            {fields.map((field, index) => (
              <Card key={field.id}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <h4 className="text-base font-medium">Refacción {index + 1}</h4>
                    {(isBrakeService || fields.length > 1) && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removePart(index)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        title={isBrakeService ? "Eliminar esta refacción" : "Eliminar esta refacción"}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`parts.${index}.partName`}>Nombre de la Refacción *</Label>
                      <Input
                        {...form.register(`parts.${index}.partName`)}
                        placeholder="Ej: Pastillas de freno delanteras"
                      />
                      {form.formState.errors.parts?.[index]?.partName && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.parts[index]?.partName?.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`parts.${index}.partNumber`}>Número de Parte</Label>
                      <Input
                        {...form.register(`parts.${index}.partNumber`)}
                        placeholder="Ej: BP-001-2024"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`parts.${index}.quantity`}>Cantidad *</Label>
                      <Input
                        type="number"
                        min="1"
                        {...form.register(`parts.${index}.quantity`, { valueAsNumber: true })}
                        placeholder="1"
                      />
                      {form.formState.errors.parts?.[index]?.quantity && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.parts[index]?.quantity?.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`parts.${index}.unitCost`}>Costo Unitario (MXN) *</Label>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        {...form.register(`parts.${index}.unitCost`, { valueAsNumber: true })}
                        placeholder="0.00"
                      />
                      {form.formState.errors.parts?.[index]?.unitCost && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.parts[index]?.unitCost?.message}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`parts.${index}.supplier`}>Proveedor</Label>
                      <Input
                        {...form.register(`parts.${index}.supplier`)}
                        placeholder="Ej: AutoPartes México"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`parts.${index}.estimatedArrival`}>Fecha Estimada de Llegada</Label>
                      <Input
                        type="date"
                        {...form.register(`parts.${index}.estimatedArrival`)}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`parts.${index}.status`}>Estado *</Label>
                      <Select
                        value={watchedParts[index]?.status}
                        onValueChange={(value: any) => form.setValue(`parts.${index}.status`, value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecciona un estado" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="pending">
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4" />
                              Pendiente
                            </div>
                          </SelectItem>
                          <SelectItem value="ordered">
                            <div className="flex items-center gap-2">
                              <Package className="h-4 w-4" />
                              Ordenada
                            </div>
                          </SelectItem>
                          <SelectItem value="in-transit">
                            <div className="flex items-center gap-2">
                              <Loader2 className="h-4 w-4" />
                              En Tránsito
                            </div>
                          </SelectItem>
                          <SelectItem value="arrived">
                            <div className="flex items-center gap-2">
                              <CheckCircle className="h-4 w-4" />
                              Llegó
                            </div>
                          </SelectItem>
                          <SelectItem value="installed">
                            <div className="flex items-center gap-2">
                              <CheckCircle className="h-4 w-4" />
                              Instalada
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label>Estado Actual</Label>
                      <div className="flex items-center gap-2">
                        <Badge className={getStatusColor(watchedParts[index]?.status)} variant="secondary">
                          {getStatusIcon(watchedParts[index]?.status)}
                          <span className="ml-1">{getStatusLabel(watchedParts[index]?.status)}</span>
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`parts.${index}.notes`}>Notas</Label>
                    <Textarea
                      {...form.register(`parts.${index}.notes`)}
                      placeholder="Notas adicionales sobre esta refacción..."
                      rows={2}
                    />
                  </div>

                  <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded">
                    <p>
                      <strong>Costo total de esta refacción:</strong> {formatCurrency((watchedParts[index]?.quantity || 0) * (watchedParts[index]?.unitCost || 0))}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Resumen de Refacciones
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-gray-500">Total de Refacciones:</p>
                  <p className="font-medium">{watchedParts.length}</p>
                </div>
                <div>
                  <p className="text-gray-500">Cantidad Total:</p>
                  <p className="font-medium">{watchedParts.reduce((sum, part) => sum + (part.quantity || 0), 0)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Costo Total:</p>
                  <p className="font-bold text-lg">{formatCurrency(totalCost)}</p>
                </div>
                <div>
                  <p className="text-gray-500">Pendientes:</p>
                  <p className="font-medium text-red-600">
                    {watchedParts.filter(part => part.status === 'pending' || part.status === 'ordered').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancelar
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Actualizar Refacciones
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
