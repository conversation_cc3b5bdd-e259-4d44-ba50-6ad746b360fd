import { Request, Response } from 'express';
import {
  createNote,
  getNotesByUser,
  getNoteById,
  updateNoteById,
  deleteNoteById,
  getNotesByAdmissionRequest,
} from '../services/requestNotes';
import { Types } from 'mongoose';

export const createNoteController = async (req: Request, res: Response): Promise<Response> => {
  const userId = new Types.ObjectId(req.authUser.userId); // assuming user is attached by auth middleware
  const { content, admissionRequestId } = req.body;

  const note = await createNote(
    {
      content,
      admissionRequest: admissionRequestId ? new Types.ObjectId(admissionRequestId) : undefined,
    },
    userId
  );

  return res.status(201).json(note);
};

export const getNotesByUserController = async (req: Request, res: Response): Promise<Response> => {
  const userId = new Types.ObjectId(req.authUser.userId);
  const notes = await getNotesByUser(userId);
  return res.json(notes);
};

export const getNoteByIdController = async (req: Request, res: Response): Promise<Response> => {
  const userId = new Types.ObjectId(req.authUser.userId);
  const noteId = new Types.ObjectId(req.params.id);

  const note = await getNoteById(noteId, userId);
  return res.json(note);
};

export const updateNoteByIdController = async (req: Request, res: Response): Promise<Response> => {
  const userId = new Types.ObjectId(req.authUser.userId);
  const noteId = new Types.ObjectId(req.params.id);
  const updateData = req.body;

  const updatedNote = await updateNoteById(noteId, userId, updateData);
  return res.json(updatedNote);
};

export const deleteNoteByIdController = async (req: Request, res: Response): Promise<Response> => {
  const userId = new Types.ObjectId(req.authUser.userId);
  const noteId = new Types.ObjectId(req.params.id);

  await deleteNoteById(noteId, userId);
  return res.status(204).send();
};

export const getNotesByAdmissionRequestController = async (
  req: Request,
  res: Response
): Promise<Response> => {
  const { admissionRequestId } = req.params;

  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 10;

  try {
    const paginatedNotes = await getNotesByAdmissionRequest(
      new Types.ObjectId(admissionRequestId),
      page,
      limit
    );

    return res.json(paginatedNotes);
  } catch (error) {
    return res.status(500).json({ message: 'Error fetching notes' });
  }
};
