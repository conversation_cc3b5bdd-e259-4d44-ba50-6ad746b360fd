import { useState, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface ServicePhase {
  name: string;
  completed: boolean;
  startTime?: string;
  endTime?: string;
  duration?: number;
}

interface ServiceProgress {
  serviceId: string;
  serviceName: string;
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  currentPhase?: string;
  progress: number;
  timeSpent: number;
  estimatedTimeRemaining: number;
  isPaused: boolean;
  pauseReason?: string;
  lastActivity: string;
  phases: ServicePhase[];
}

interface ServiceCompletionData {
  notes?: string;
  qualityCheckPassed?: boolean;
  actualCost?: number;
  evidenceNotes?: string;
  completionPhotos?: File[];
  completionVideos?: File[];
  qualityCheckPhotos?: File[];
}

export const useRealTimeTracking = () => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const startServiceTracking = useCallback(async (
    serviceId: string,
    technicianId?: string
  ): Promise<boolean> => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/corrective-maintenance/services/${serviceId}/start-tracking`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ technicianId }),
      });

      if (!response.ok) {
        throw new Error('Error starting service tracking');
      }

      toast({
        title: 'Éxito',
        description: 'Seguimiento del servicio iniciado correctamente',
      });

      return true;
    } catch (error) {
      console.error('Error starting service tracking:', error);
      toast({
        title: 'Error',
        description: 'No se pudo iniciar el seguimiento del servicio',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const updateServicePhase = useCallback(async (
    serviceId: string,
    phase: string,
    evidence?: {
      photos?: File[];
      videos?: File[];
      notes?: string;
    },
    technicianId?: string
  ): Promise<boolean> => {
    setIsLoading(true);
    try {
      const formData = new FormData();
      formData.append('phase', phase);

      if (evidence?.notes) {
        formData.append('notes', evidence.notes);
      }

      if (technicianId) {
        formData.append('technicianId', technicianId);
      }

      // Add evidence files
      if (evidence?.photos) {
        evidence.photos.forEach((photo) => {
          formData.append('evidence', photo);
        });
      }

      if (evidence?.videos) {
        evidence.videos.forEach((video) => {
          formData.append('evidence', video);
        });
      }

      const response = await fetch(`/api/corrective-maintenance/services/${serviceId}/phase`, {
        method: 'PATCH',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Error updating service phase');
      }

      toast({
        title: 'Éxito',
        description: 'Fase del servicio actualizada correctamente',
      });

      return true;
    } catch (error) {
      console.error('Error updating service phase:', error);
      toast({
        title: 'Error',
        description: 'No se pudo actualizar la fase del servicio',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const pauseService = useCallback(async (
    serviceId: string,
    pauseReason: string
  ): Promise<boolean> => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/corrective-maintenance/services/${serviceId}/pause`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pauseReason }),
      });

      if (!response.ok) {
        throw new Error('Error pausing service');
      }

      toast({
        title: 'Éxito',
        description: 'Servicio pausado correctamente',
      });

      return true;
    } catch (error) {
      console.error('Error pausing service:', error);
      toast({
        title: 'Error',
        description: 'No se pudo pausar el servicio',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const resumeService = useCallback(async (serviceId: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/corrective-maintenance/services/${serviceId}/resume`, {
        method: 'POST',
      });

      if (!response.ok) {
        throw new Error('Error resuming service');
      }

      toast({
        title: 'Éxito',
        description: 'Servicio reanudado correctamente',
      });

      return true;
    } catch (error) {
      console.error('Error resuming service:', error);
      toast({
        title: 'Error',
        description: 'No se pudo reanudar el servicio',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const completeServiceWithEvidence = useCallback(async (
    serviceId: string,
    completionData: ServiceCompletionData,
    technicianId?: string
  ): Promise<boolean> => {
    setIsLoading(true);
    try {
      const formData = new FormData();

      if (completionData.notes) {
        formData.append('notes', completionData.notes);
      }

      if (completionData.qualityCheckPassed !== undefined) {
        formData.append('qualityCheckPassed', completionData.qualityCheckPassed.toString());
      }

      if (completionData.actualCost !== undefined) {
        formData.append('actualCost', completionData.actualCost.toString());
      }

      if (completionData.evidenceNotes) {
        formData.append('evidenceNotes', completionData.evidenceNotes);
      }

      if (technicianId) {
        formData.append('technicianId', technicianId);
      }

      // Add completion photos
      if (completionData.completionPhotos) {
        completionData.completionPhotos.forEach((photo) => {
          formData.append('completionPhotos', photo);
        });
      }

      // Add completion videos
      if (completionData.completionVideos) {
        completionData.completionVideos.forEach((video) => {
          formData.append('completionVideos', video);
        });
      }

      // Add quality check photos
      if (completionData.qualityCheckPhotos) {
        completionData.qualityCheckPhotos.forEach((photo) => {
          formData.append('qualityCheckPhotos', photo);
        });
      }

      const response = await fetch(`/api/corrective-maintenance/services/${serviceId}/complete-with-evidence`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Error completing service');
      }

      toast({
        title: 'Éxito',
        description: 'Servicio completado correctamente',
      });

      return true;
    } catch (error) {
      console.error('Error completing service:', error);
      toast({
        title: 'Error',
        description: 'No se pudo completar el servicio',
        variant: 'destructive',
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const getOrderProgress = useCallback(async (orderId: string): Promise<ServiceProgress[]> => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/corrective-maintenance/orders/${orderId}/progress`);

      if (!response.ok) {
        throw new Error('Error getting order progress');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error getting order progress:', error);
      toast({
        title: 'Error',
        description: 'No se pudo obtener el progreso de la orden',
        variant: 'destructive',
      });
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const getServiceDetails = useCallback(async (serviceId: string): Promise<any> => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/corrective-maintenance/services/${serviceId}/details`);

      if (!response.ok) {
        throw new Error('Error getting service details');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error getting service details:', error);
      toast({
        title: 'Error',
        description: 'No se pudieron obtener los detalles del servicio',
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const getActiveServicesDashboard = useCallback(async (workshopId?: string): Promise<any> => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams();
      if (workshopId) {
        params.append('workshopId', workshopId);
      }

      const response = await fetch(`/api/corrective-maintenance/dashboard/active-services?${params}`);

      if (!response.ok) {
        throw new Error('Error getting active services dashboard');
      }

      const data = await response.json();
      return data.data;
    } catch (error) {
      console.error('Error getting active services dashboard:', error);
      toast({
        title: 'Error',
        description: 'No se pudo obtener el dashboard de servicios activos',
        variant: 'destructive',
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  return {
    isLoading,
    startServiceTracking,
    updateServicePhase,
    pauseService,
    resumeService,
    completeServiceWithEvidence,
    getOrderProgress,
    getServiceDetails,
    getActiveServicesDashboard,
  };
};
